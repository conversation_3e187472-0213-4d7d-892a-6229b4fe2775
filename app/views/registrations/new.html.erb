<div class="login-container">
  <h1>Create Account</h1>
  <%= form_with model: @user, url: register_path do |form| %>
    <% if @user.errors.any? %>
      <div class="error-messages">
        <h2><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>
        <ul>
          <% @user.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    <% end %>

    <div class="field">
      <%= form.label :email_address %>
      <%= form.email_field :email_address, required: true, autofocus: true %>
    </div>

    <div class="field">
      <%= form.label :password %>
      <%= form.password_field :password, required: true %>
    </div>

    <div class="field">
      <%= form.label :password_confirmation %>
      <%= form.password_field :password_confirmation, required: true %>
    </div>

    <div class="actions">
      <%= form.submit "Sign Up" %>
    </div>
  <% end %>

  <p>Already have an account? <%= link_to "Sign in", login_path %></p>
</div>
