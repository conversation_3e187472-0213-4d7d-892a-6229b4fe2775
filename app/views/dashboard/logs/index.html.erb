<div class="prose max-w-none">
  <div class="flex justify-between items-center mb-4">
    <h1 class="mb-0">Application Logs</h1>
    <% if @current_log.present? %>
      <div class="flex items-center gap-2">
        <%= link_to "Download Log", dashboard_logs_download_path(log_file: @current_log), class: "btn btn-sm btn-outline" %>
        <%= button_to "Clear Log", dashboard_logs_clear_path(log_file: @current_log),
              method: :delete,
              class: "btn btn-sm btn-error btn-outline",
              data: { confirm: "Are you sure you want to clear the '#{@current_log}' file? This action cannot be undone." } %>
      </div>
    <% end %>
  </div>

  <!-- DaisyUI Tabs with unique name for this log group -->
  <div class="tabs tabs-lift">
    <% @log_files.each_with_index do |log_file, index| %>
      <% is_active = (log_file == @current_log) %>
      <label class="tab">
        <input type="radio" name="log_tabs_<%= log_file.gsub('.', '_') %>" <%= 'checked="checked"' if is_active %>
               onchange="window.location.href='<%= dashboard_logs_path(log_file: log_file) %>'" />
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="my-1.5 inline-block size-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.375 19.5h17.25m-17.25 0a1.125 1.125 0 0 1-1.125-1.125M3.375 19.5h7.5c.621 0 1.125-.504 1.125-1.125m-9.75 0V5.625m0 12.75v-1.5c0-.621.504-1.125 1.125-1.125m18.375 2.625V5.625m0 12.75c0 .621-.504 1.125-1.125 1.125m1.125-1.125v-1.5c0-.621-.504-1.125-1.125-1.125m0 3.75h-7.5A1.125 1.125 0 0 1 12 18.375m9.75-12.75c0-.621-.504-1.125-1.125-1.125H3.375c-.621 0-1.125.504-1.125 1.125m19.5 0v1.5c0 .621-.504 1.125-1.125 1.125M2.25 5.625v1.5c0 .621.504 1.125 1.125 1.125m0 0h17.25m-17.25 0h7.5c.621 0 1.125.504 1.125 1.125M3.375 8.25c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125m17.25-3.75h-7.5c-.621 0-1.125.504-1.125 1.125m8.625-1.125c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125v1.5c0 .621.504 1.125 1.125 1.125M12 10.875v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 10.875c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125M13.125 12h7.5m-7.5 0c-.621 0-1.125.504-1.125 1.125M20.625 12c.621 0 1.125.504 1.125 1.125v1.5c0 .621-.504 1.125-1.125 1.125m-17.25 0h7.5M12 14.625v-1.5m0 1.5c0 .621-.504 1.125-1.125 1.125M12 14.625c0 .621.504 1.125 1.125 1.125m-2.25 0c.621 0 1.125.504 1.125 1.125m0 1.5v-1.5m0 0c0-.621.504-1.125 1.125-1.125m0 0h7.5" />
        </svg>
        <%= log_file.chomp('.log').humanize %>
      </label>
      <% if is_active %>
        <div class="tab-content bg-base-100 border-base-300 p-6">
          <!-- File Details -->
          <% if @file_details %>
            <div class="stats shadow mb-4">
              <div class="stat">
                <div class="stat-title">File Size</div>
                <div class="stat-value text-sm"><%= @file_details[:size_human] %></div>
                <div class="stat-desc"><%= number_with_delimiter(@file_details[:size]) %> bytes</div>
              </div>
              <div class="stat">
                <div class="stat-title">Total Lines</div>
                <div class="stat-value text-sm"><%= number_with_delimiter(@file_details[:total_lines]) %></div>
                <div class="stat-desc">Showing <%= @pagy.count %> lines (page <%= @pagy.page %> of <%= @pagy.pages %>)</div>
              </div>
              <div class="stat">
                <div class="stat-title">Last Modified</div>
                <div class="stat-value text-sm"><%= @file_details[:last_modified_human] %> ago</div>
                <div class="stat-desc"><%= @file_details[:last_modified].strftime("%Y-%m-%d %H:%M:%S") %></div>
              </div>
            </div>
          <% end %>

          <!-- Log Content -->
          <div class="bg-success-content text-success p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
            <% if @log_lines.any? %>
              <% @log_lines.each do |line| %>
                <div class="whitespace-pre-wrap break-words"><%= line.html_safe %></div>
              <% end %>
            <% else %>
              <div class="text-success">Log file is empty.</div>
            <% end %>
          </div>
        </div>
      <% end %>
    <% end %>
  </div>
</div>
<% if @pagy.pages > 1 %>
  <div class="mt-4">
    <%== pagy_nav(@pagy) %>
  </div>
<% end %>

