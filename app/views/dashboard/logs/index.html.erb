<div class="prose max-w-none">
  <div class="flex justify-between items-center mb-4">
    <h1 class="mb-0">Application Logs</h1>
    <% if @current_log.present? %>
      <div class="flex items-center gap-2">
        <%= link_to "Download Log", dashboard_logs_download_path(log_file: @current_log), class: "btn btn-sm btn-outline" %>
        <%= button_to "Clear Log", dashboard_logs_clear_path(log_file: @current_log),
              method: :delete,
              class: "btn btn-sm btn-error btn-outline",
              data: { confirm: "Are you sure you want to clear the '#{@current_log}' file? This action cannot be undone." } %>
      </div>
    <% end %>
  </div>

  <div role="tablist" class="tabs tabs-lifted">
    <% @log_files.each_with_index do |log_file, index| %>
      <% is_active = (log_file == @current_log) %>
      <%= link_to log_file.chomp('.log').humanize,
            dashboard_logs_path(log_file: log_file),
            role: "tab",
            class: "tab #{'tab-active' if is_active}" %>
    <% end %>
    <%# This empty tab creates the lifted effect for the content area %>
    <div role="tab" class="tab [--tab-border-color:transparent]"></div>

    <div class="tab-content bg-base-100 border-base-300 rounded-box p-4 -mt-px">
      <pre class="font-mono text-sm whitespace-pre-wrap break-words"><% if @log_lines.any? %>
<%= @log_lines.join.html_safe %>
<% else %>
Log file is empty.
<% end %></pre>
    </div>
  </div>

  <% if @pagy.pages > 1 %>
    <div class="mt-4">
      <%== pagy_nav(@pagy) %>
    </div>
  <% end %>
</div>

<style>
  pre {
    background-color: transparent !important;
  }
</style>