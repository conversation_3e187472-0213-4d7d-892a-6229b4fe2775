<h1>Media Library</h1>

<!-- Upload Area -->
<div class="mb-6" data-controller="media-upload">
  <%= form_with url: dashboard_media_items_path, method: :post, multipart: true, 
      class: "border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:border-primary transition bg-base-100",
      data: { 
        media_upload_target: "dropzone", 
        action: "dragover->media-upload#dragOver dragleave->media-upload#dragLeave drop->media-upload#drop" 
      } do |f| %>
    
    <div class="flex items-center justify-center gap-4">
      <svg class="h-10 w-10 text-base-content opacity-50" stroke="currentColor" fill="none" viewBox="0 0 48 48">
        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
      <div class="text-left">
        <p class="text-sm">
          <label for="media_library_upload" class="cursor-pointer text-primary hover:text-primary-focus font-semibold">
            Click to upload
          </label>
          or drag and drop files here
        </p>
        <p class="text-xs text-base-content opacity-60">PNG, JPG, GIF up to 10MB • Auto-uploads on selection</p>
      </div>
      
      <input type="file" 
             id="media_library_upload"
             name="files[]" 
             multiple 
             class="hidden" 
             data-media-upload-target="fileInput"
             data-action="change->media-upload#handleFilesAndSubmit">
    </div>
    
    <div data-media-upload-target="preview" class="mt-4"></div>
    <div data-media-upload-target="status" class="mt-2 text-sm"></div>
  <% end %>
</div>

<div class="mb-6 flex justify-between items-center">
  <div class="flex gap-4">
    <%= form_with url: dashboard_media_items_path, method: :get, class: "flex gap-2" do |f| %>
      <%= f.text_field :search, placeholder: "Search media...", value: params[:search], class: "input input-bordered" %>
      <%= f.select :filter, options_for_select([['All Types', ''], ['Images', 'image'], ['Documents', 'document'], ['Videos', 'video']], params[:filter]), {}, class: "select select-bordered" %>
      <%= f.submit "Filter", class: "btn btn-primary" %>
    <% end %>
  </div>
</div>

<% if @media_items.any? %>
  <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
    <% @media_items.each do |media_item| %>
      <div class="card bg-base-100 shadow-xl">
        <figure class="h-40 bg-base-200">
          <% if media_item.image? && media_item.file.attached? %>
            <%= image_tag media_item.file, class: "object-cover w-full h-full" %>
          <% else %>
            <div class="flex items-center justify-center h-full">
              <span class="text-4xl">📄</span>
            </div>
          <% end %>
        </figure>
        <div class="card-body p-3">
          <h3 class="card-title text-sm truncate" title="<%= media_item.title %>">
            <%= media_item.title %>
          </h3>
          <p class="text-xs text-gray-500"><%= media_item.file_type&.capitalize %></p>
          <div class="card-actions justify-end mt-2">
            <button type="button" class="btn btn-xs btn-ghost" onclick="editMedia_<%= media_item.id %>.showModal()">
              Edit
            </button>
            <button type="button" class="btn btn-xs btn-error" onclick="deleteMedia_<%= media_item.id %>.showModal()">
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Delete Confirmation Modal -->
      <dialog id="deleteMedia_<%= media_item.id %>" class="modal modal-bottom sm:modal-middle">
        <div class="modal-box">
          <h3 class="font-bold text-lg">Delete Media?</h3>
          <p class="py-4">Are you sure you want to delete "<%= media_item.title %>"? This action cannot be undone.</p>
          <div class="modal-action">
            <button type="button" class="btn" onclick="deleteMedia_<%= media_item.id %>.close()">Cancel</button>
            <%= button_to "Delete", dashboard_media_item_path(media_item), method: :delete, class: "btn btn-error" %>
          </div>
        </div>
        <form method="dialog" class="modal-backdrop">
          <button>close</button>
        </form>
      </dialog>

      <!-- Edit Modal for each item -->
      <dialog id="editMedia_<%= media_item.id %>" class="modal">
        <div class="modal-box">
          <h3 class="font-bold text-lg">Edit Media</h3>
          <%= form_with model: [:dashboard, media_item], method: :patch do |f| %>
            <div class="form-control mb-4">
              <%= f.label :title, class: "label" %>
              <%= f.text_field :title, class: "input input-bordered w-full" %>
            </div>
            <div class="form-control mb-4">
              <%= f.label :alt_text, "Alt Text (for images)", class: "label" %>
              <%= f.text_field :alt_text, class: "input input-bordered w-full" %>
            </div>
            <div class="modal-action">
              <button type="button" class="btn" onclick="editMedia_<%= media_item.id %>.close()">Cancel</button>
              <%= f.submit "Save", class: "btn btn-primary" %>
            </div>
          <% end %>
        </div>
        <form method="dialog" class="modal-backdrop">
          <button>close</button>
        </form>
      </dialog>
    <% end %>
  </div>

  <div class="mt-6 flex justify-center">
    <%== pagy_nav(@pagy) if @pagy.pages > 1 %>
  </div>
<% else %>
  <div class="text-center py-12">
    <p class="text-gray-500 mb-4">No media files yet. Upload your first file using the upload box above!</p>
  </div>
<% end %>
