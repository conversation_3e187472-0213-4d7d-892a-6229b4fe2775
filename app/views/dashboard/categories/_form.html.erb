<%= form_with(model: [:dashboard, category], data: { controller: "slug" }) do |form| %>
  <% if category.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(category.errors.count, "error") %> prohibited this category from being saved:</h2>
      <ul>
        <% category.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :name, class: "label" do %>
      <span class="label-text">Name</span>
    <% end %>
    <%= form.text_field :name, class: "input input-bordered w-full max-w-xs", data: { slug_target: "source", action: "input->slug#generate" } %>
  </div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :slug, class: "label" do %>
      <span class="label-text">Slug</span>
    <% end %>
    <%= form.text_field :slug, class: "input input-bordered w-full max-w-xs", data: { slug_target: "target" } %>
  </div>

  <div class="mt-6">
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>
