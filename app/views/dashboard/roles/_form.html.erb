<%= form_with(model: [:dashboard, role], class: "space-y-6") do |f| %>
  <% if role.errors.any? %>
    <div class="alert alert-error">
      <div>
        <h3 class="font-bold"><%= pluralize(role.errors.count, "error") %> prohibited this role from being saved:</h3>
        <ul class="list-disc list-inside">
          <% role.errors.full_messages.each do |message| %>
            <li><%= message %></li>
          <% end %>
        </ul>
      </div>
    </div>
  <% end %>

  <div class="form-control w-full max-w-md">
    <%= f.label :name, class: "label" do %>
      <span class="label-text">Role Name</span>
    <% end %>
    <%= f.text_field :name, class: "input input-bordered w-full", disabled: role.system_defined? %>
    <% if role.system_defined? %>
      <p class="text-xs text-gray-500 mt-1">System roles cannot be renamed.</p>
    <% end %>
  </div>

  <div class="divider">Permissions</div>

  <div class="overflow-x-auto">
    <table class="table table-xs w-full">
      <thead>
        <tr>
          <th>Resource</th>
          <% available_actions.each do |action| %>
            <th class="text-center"><%= action.humanize %></th>
          <% end %>
        </tr>
      </thead>
      <tbody>
        <% available_resources.each do |resource| %>
          <tr>
            <td class="font-bold"><%= resource %></td>
            <% available_actions.each do |action| %>
              <td class="text-center">
                <% permission = role.permissions.find { |p| p.resource == resource && p.action == action } %>
                <% if permission %>
                  <input type="hidden" name="role[permissions_attributes][][id]" value="<%= permission.id %>">
                  <input type="hidden" name="role[permissions_attributes][][resource]" value="<%= resource %>">
                  <input type="hidden" name="role[permissions_attributes][][action]" value="<%= action %>">
                  <input type="checkbox" name="role[permissions_attributes][][_destroy]" value="0" checked class="checkbox checkbox-xs" onchange="this.previousElementSibling.value = this.checked ? '<%= action %>' : ''">
                  <!-- Hacky way to handle destroy. Better: use a proper nested form builder or JS. 
                       Actually, simple checkbox logic:
                       If checked, we want a permission record.
                       If unchecked, we want to destroy it if it exists.
                  -->
                  <!-- Let's try a simpler approach: Just a list of checkboxes. 
                       The controller expects nested attributes. 
                       This matrix UI is hard to map directly to nested attributes without JS or complex form logic.
                  -->
                <% else %>
                   <!-- New permission -->
                <% end %>
              </td>
            <% end %>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
  
  <!-- Alternative: Simple list of permissions -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    <% available_resources.each do |resource| %>
      <div class="card bg-base-100 shadow-sm border">
        <div class="card-body p-4">
          <h3 class="card-title text-sm"><%= resource %></h3>
          <div class="flex flex-wrap gap-2">
            <% available_actions.each do |action| %>
              <% permission = role.permissions.find { |p| p.resource == resource && p.action == action } %>
              <label class="cursor-pointer label p-0 gap-2">
                <% if permission %>
                  <input type="hidden" name="role[permissions_attributes][<%= resource %>_<%= action %>][id]" value="<%= permission.id %>">
                  <input type="hidden" name="role[permissions_attributes][<%= resource %>_<%= action %>][resource]" value="<%= resource %>">
                  <input type="hidden" name="role[permissions_attributes][<%= resource %>_<%= action %>][action]" value="<%= action %>">
                  <input type="checkbox" name="role[permissions_attributes][<%= resource %>_<%= action %>][_destroy]" value="1" class="checkbox checkbox-xs" onchange="this.value = this.checked ? '0' : '1'">
                  <span class="label-text text-xs"><%= action.humanize %></span>
                  <!-- If checked (value 0 for destroy), it stays. If unchecked (value 1), it gets destroyed. 
                       Wait, checkbox checked means we WANT the permission.
                       So checked -> _destroy = 0. Unchecked -> _destroy = 1.
                       Initial state: checked.
                  -->
                  <script>
                    // Set initial state of checkbox based on existence
                    document.currentScript.previousElementSibling.previousElementSibling.checked = true;
                    document.currentScript.previousElementSibling.previousElementSibling.value = "0"; 
                  </script>
                <% else %>
                  <input type="hidden" name="role[permissions_attributes][<%= resource %>_<%= action %>][resource]" value="<%= resource %>">
                  <input type="hidden" name="role[permissions_attributes][<%= resource %>_<%= action %>][action]" value="<%= action %>">
                  <input type="checkbox" name="role[permissions_attributes][<%= resource %>_<%= action %>][_destroy]" value="1" class="checkbox checkbox-xs" onchange="this.value = this.checked ? '0' : '1'">
                  <span class="label-text text-xs"><%= action.humanize %></span>
                <% end %>
              </label>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

  <div class="flex justify-end gap-2 mt-6">
    <%= link_to "Cancel", dashboard_roles_path, class: "btn btn-ghost" %>
    <%= f.submit class: "btn btn-primary" %>
  </div>
<% end %>
