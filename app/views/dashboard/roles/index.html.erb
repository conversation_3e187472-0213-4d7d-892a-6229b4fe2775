<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Roles</h1>
    <%= link_to "New Role", new_dashboard_role_path, class: "btn btn-primary" %>
  </div>

  <div class="overflow-x-auto bg-white rounded-lg shadow">
    <table class="table w-full">
      <thead>
        <tr>
          <th>Name</th>
          <th>Slug</th>
          <th>System Defined</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <% @roles.each do |role| %>
          <tr class="hover">
            <td class="font-medium"><%= role.name %></td>
            <td><%= role.slug %></td>
            <td>
              <% if role.system_defined? %>
                <span class="badge badge-info">Yes</span>
              <% else %>
                <span class="badge badge-ghost">No</span>
              <% end %>
            </td>
            <td>
              <div class="flex gap-2">
                <%= link_to "Edit", edit_dashboard_role_path(role), class: "btn btn-sm btn-ghost" %>
                <% unless role.system_defined? %>
                  <%= button_to "Delete", dashboard_role_path(role), method: :delete, class: "btn btn-sm btn-error btn-outline", form: { data: { turbo_confirm: "Are you sure?" } } %>
                <% end %>
              </div>
            </td>
          </tr>
        <% end %>
      </tbody>
    </table>
  </div>
</div>
