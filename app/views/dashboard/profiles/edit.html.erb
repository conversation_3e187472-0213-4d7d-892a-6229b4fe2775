<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-6">Edit Profile</h1>

  <div class="card bg-base-100 shadow-xl max-w-2xl">
    <div class="card-body">
      <%= form_with model: @user, url: dashboard_profile_path, method: :patch do |form| %>
        <% if @user.errors.any? %>
          <div class="alert alert-error mb-4">
            <h2 class="font-bold"><%= pluralize(@user.errors.count, "error") %> prohibited this profile from being saved:</h2>
            <ul>
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="form-control mb-4" data-controller="media-picker" data-media-picker-preview-classes-value="w-24 h-24 rounded-full object-cover">
          <%= form.label :avatar, "Profile Picture", class: "label" %>
          <% if @user.avatar.attached? %>
            <div class="mb-2" data-media-picker-target="preview">
              <%= image_tag @user.avatar, class: "w-24 h-24 rounded-full object-cover" %>
            </div>
          <% else %>
            <div class="mb-2" data-media-picker-target="preview">
              <div class="w-24 h-24 rounded-full bg-primary flex items-center justify-center text-primary-content text-2xl font-bold">
                <%= @user.display_name[0].upcase %>
              </div>
            </div>
          <% end %>
          
          <button type="button" class="btn btn-sm btn-primary" data-action="click->media-picker#openModal">
            Select from Library
          </button>
          
          <%= form.hidden_field :avatar_media_id, data: { media_picker_target: "selectedInput" } %>
        </div>

        <div class="form-control mb-4">
          <%= form.label :name, class: "label" %>
          <%= form.text_field :name, class: "input input-bordered w-full", placeholder: "Your full name" %>
        </div>

        <div class="form-control mb-4">
          <%= form.label :email_address, class: "label" %>
          <%= form.email_field :email_address, class: "input input-bordered w-full", required: true %>
        </div>

        <div class="divider">Change Password (optional)</div>

        <div class="form-control mb-4">
          <%= form.label :password, "New Password", class: "label" %>
          <%= form.password_field :password, class: "input input-bordered w-full", placeholder: "Leave blank to keep current password" %>
        </div>

        <div class="form-control mb-4">
          <%= form.label :password_confirmation, class: "label" %>
          <%= form.password_field :password_confirmation, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control mt-6">
          <%= form.submit "Update Profile", class: "btn btn-primary" %>
          <%= link_to "Cancel", dashboard_root_path, class: "btn btn-ghost" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<%= render "dashboard/shared/media_picker_modal" %>
