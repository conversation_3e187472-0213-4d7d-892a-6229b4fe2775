<div class="container mx-auto px-4 py-8">
  <h1 class="text-3xl font-bold mb-6">Edit User Role</h1>

  <div class="card bg-base-100 shadow-xl max-w-2xl">
    <div class="card-body">
      <%= form_with model: @user, url: dashboard_user_path(@user), method: :patch do |form| %>
        <% if @user.errors.any? %>
          <div class="alert alert-error mb-4">
            <h2 class="font-bold"><%= pluralize(@user.errors.count, "error") %> prohibited this user from being saved:</h2>
            <ul>
              <% @user.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        <% end %>

        <div class="form-control mb-4">
          <%= form.label :email_address, class: "label" %>
          <span class="text-lg"><%= @user.email_address %></span>
        </div>

        <div class="form-control mb-4">
          <%= form.label :role_id, "Role", class: "label" %>
          <%= form.collection_select :role_id, Role.all, :id, :name, {}, class: "select select-bordered w-full" %>
        </div>

        <div class="divider">Change Password (optional)</div>

        <div class="form-control mb-4">
          <%= form.label :password, class: "label" %>
          <%= form.password_field :password, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control mb-4">
          <%= form.label :password_confirmation, class: "label" %>
          <%= form.password_field :password_confirmation, class: "input input-bordered w-full" %>
        </div>

        <div class="form-control mt-6">
          <%= form.submit "Update User", class: "btn btn-primary" %>
          <%= link_to "Cancel", dashboard_users_path, class: "btn btn-ghost" %>
        </div>
      <% end %>
    </div>
  </div>
</div>
