<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">User Management</h1>
    <%= link_to "Create New User", new_dashboard_user_path, class: "btn btn-primary" %>
  </div>

  <div class="card bg-base-100 shadow-xl">
    <div class="card-body">
      <div class="overflow-x-auto">
        <table class="table table-zebra w-full">
          <thead>
            <tr>
              <th>Email</th>
              <th>Role</th>
              <th>Created At</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <% @users.each do |user| %>
              <tr>
                <td><%= user.email_address %></td>
                <td>
                  <span class="badge <%= role_badge_class(user.role.slug) %>">
                    <%= user.role.name %>
                  </span>
                </td>
                <td><%= user.created_at.strftime("%B %d, %Y") %></td>
                <td>
                  <%= link_to "Edit", edit_dashboard_user_path(user), class: "btn btn-sm btn-primary" %>
                  <% unless user == Current.user %>
                    <%= button_to "Delete", dashboard_user_path(user), method: :delete, 
                        data: { confirm: "Are you sure you want to delete this user?" }, 
                        class: "btn btn-sm btn-error" %>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
