<!-- Media Picker Modal -->
<dialog id="media_picker_modal" class="modal">
  <div class="modal-box max-w-5xl">
    <h3 class="font-bold text-lg mb-4">Select from Media Library</h3>
    
    <!-- Upload Area -->
    <div class="mb-6" data-controller="media-upload">
      <%= form_with url: dashboard_media_items_path, method: :post, multipart: true, 
          class: "border-2 border-dashed border-base-300 rounded-lg p-6 text-center hover:border-primary transition",
          data: { 
            media_upload_target: "dropzone", 
            action: "dragover->media-upload#dragOver dragleave->media-upload#dragLeave drop->media-upload#drop submit->media-upload#handleSubmit" 
          } do |f| %>
        
        <div class="mb-4">
          <svg class="mx-auto h-12 w-12 text-base-content opacity-50" stroke="currentColor" fill="none" viewBox="0 0 48 48">
            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
          </svg>
          <p class="mt-2 text-sm">
            <label for="media_upload_input" class="cursor-pointer text-primary hover:text-primary-focus font-semibold">
              Click to upload
            </label>
            or drag and drop
          </p>
          <p class="text-xs text-base-content opacity-60 mt-1">PNG, JPG, GIF up to 10MB • Auto-uploads and selects</p>
        </div>
        
        <input type="file" 
               id="media_upload_input"
               name="files[]" 
               multiple 
               class="hidden" 
               data-media-upload-target="fileInput"
               data-action="change->media-upload#handleFilesAndSubmit">
        
        <div data-media-upload-target="preview" class="mt-4"></div>
        <div data-media-upload-target="status" class="mt-2 text-sm"></div>
      <% end %>
    </div>
    
    <div class="divider">OR SELECT EXISTING</div>
    
    <div class="mb-4 flex gap-2">
      <%= form_with url: "#", method: :get, class: "flex gap-2 flex-1", data: { turbo: false } do |f| %>
        <%= f.text_field :search, placeholder: "Search media...", class: "input input-bordered input-sm flex-1", 
            id: "media_picker_search" %>
        <%= f.select :filter, options_for_select([['All Types', ''], ['Images', 'image'], ['Documents', 'document'], ['Videos', 'video']]), 
            {}, class: "select select-bordered select-sm", id: "media_picker_filter" %>
      <% end %>
    </div>

    <div class="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 max-h-96 overflow-y-auto" id="media_picker_grid">
      <% MediaItem.images.recent.limit(50).each do |media_item| %>
        <div class="cursor-pointer hover:opacity-75 transition border-2 border-transparent hover:border-primary rounded" 
             onclick="window.selectMediaFromPicker('<%= media_item.id %>', '<%= url_for(media_item.file) if media_item.file.attached? %>', '<%= j media_item.title %>')">
          <div class="card bg-base-100 shadow">
            <figure class="h-24 bg-base-200">
              <% if media_item.image? && media_item.file.attached? %>
                <%= image_tag media_item.file, class: "object-cover w-full h-full" %>
              <% else %>
                <div class="flex items-center justify-center h-full">
                  <span class="text-2xl">📄</span>
                </div>
              <% end %>
            </figure>
            <div class="card-body p-2">
              <p class="text-xs truncate" title="<%= media_item.title %>"><%= media_item.title %></p>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <div class="modal-action">
      <button type="button" class="btn" onclick="media_picker_modal.close()">Cancel</button>
    </div>
  </div>
  <form method="dialog" class="modal-backdrop">
    <button>close</button>
  </form>
</dialog>

<script>
  // Global function to handle media selection from modal
  window.selectMediaFromPicker = function(mediaId, mediaUrl, mediaTitle) {
    const modal = document.getElementById('media_picker_modal')
    if (!modal) return

    // Determine which picker opened the modal
    const activeUid = modal.dataset.activePicker
    if (!activeUid) {
      // No active picker recorded — do nothing to avoid mass-updating pickers
      modal.close()
      return
    }

    // Find the specific picker element with matching UID
    const selector = `[data-media-picker-uid="${activeUid}"]`
    const element = document.querySelector(selector)
    if (!element) {
      modal.close()
      return
    }

    const controller = window.Stimulus.getControllerForElementAndIdentifier(element, 'media-picker')

    // Update hidden input
    const input = element.querySelector('[data-media-picker-target="selectedInput"]')
    if (input) {
      input.value = mediaId
    }

    // Update preview and validate dimensions for images
    const preview = element.querySelector('[data-media-picker-target="preview"]')
    const previewClasses = element.dataset.mediaPickerPreviewClassesValue || 'max-w-xs rounded'

    if (mediaUrl) {
      const img = new Image()
      img.onload = function() {
        // Check dimensions
        const isSquare = img.naturalWidth === img.naturalHeight

        // Build preview HTML
        if (preview) {
          preview.innerHTML = `\n            <div class="relative inline-block">\n              <img src="${mediaUrl}" alt="${mediaTitle}" class="${previewClasses}">\n            </div>\n          `
        }

        // If controller exposes helpers, show or clear warning
        if (controller && typeof controller.setWarning === 'function' && typeof controller.clearWarning === 'function') {
          if (!isSquare && element.dataset.mediaPickerValidateSquare === 'true') {
            controller.setWarning('Image is not square — it may not display correctly as a favicon.')
          } else {
            controller.clearWarning()
          }
        } else if (!isSquare && preview) {
          // Fallback: show an inline warning element under the preview
          let warn = element.querySelector('[data-media-picker-target="warning"]')
          if (!warn) {
            warn = document.createElement('div')
            warn.dataset.mediaPickerTarget = 'warning'
            warn.className = 'text-yellow-600 text-sm mt-2'
            preview.insertAdjacentElement('afterend', warn)
          }
          warn.innerText = 'Image is not square — it may not display correctly as a favicon.'
          warn.style.display = 'block'
        }

        // Close modal after handling
        delete modal.dataset.activePicker
        modal.close()
      }

      img.onerror = function() {
        // Could not load image — still update preview (if possible) and clear warnings
        if (preview) {
          preview.innerHTML = `\n            <div class="relative inline-block">\n              <img src="${mediaUrl}" alt="${mediaTitle}" class="${previewClasses}">\n            </div>\n          `
        }
        if (controller && typeof controller.clearWarning === 'function') {
          controller.clearWarning()
        }
        delete modal.dataset.activePicker
        modal.close()
      }

      img.src = mediaUrl
    } else {
      // No URL (non-image) — clear preview & warnings or show placeholder
      if (preview) {
        preview.innerHTML = `\n          <div class="inline-block">\n            <span class=\"text-2xl\">📄</span>\n          </div>\n        `
      }
      if (controller && typeof controller.clearWarning === 'function') {
        controller.clearWarning()
      }
      delete modal.dataset.activePicker
      modal.close()
    }
  }
</script>
