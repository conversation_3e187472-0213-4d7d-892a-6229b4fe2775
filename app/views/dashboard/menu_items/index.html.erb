<h1 class="text-3xl font-bold mb-6">Menu Management</h1>

<!-- Location Tabs -->
<div class="tabs tabs-boxed mb-6">
  <% @locations.each do |location_key, location_name| %>
    <%= link_to location_name,
        dashboard_menu_items_path(location: location_key),
        class: "tab #{'tab-active' if @current_location == location_key}" %>
  <% end %>
</div>

<!-- Main Layout: Responsive - Side-by-side on desktop, stack on mobile with form on top -->
<div class="flex flex-col-reverse lg:flex-row gap-6 w-full">
  <!-- Add Menu Item Form: Full width on mobile (shows on top), 20% on desktop (shows on left) -->
  <div class="w-full lg:w-1/5 bg-white rounded-lg shadow-sm border border-gray-200 p-6 h-fit order-first lg:order-none">
    <h3 class="text-lg font-semibold mb-4">Add Menu Item</h3>

    <div data-controller="menu-form">
      <!-- Menu Item Type Tabs -->
      <div class="tabs tabs-boxed mb-4 w-full">
        <a class="tab tab-active text-xs" data-action="click->menu-form#showCustom" data-menu-form-target="customTab">Custom</a>
        <a class="tab text-xs" data-action="click->menu-form#showPages" data-menu-form-target="pagesTab">Pages</a>
        <a class="tab text-xs" data-action="click->menu-form#showCategories" data-menu-form-target="categoriesTab">Categories</a>
        <a class="tab text-xs" data-action="click->menu-form#showTags" data-menu-form-target="tagsTab">Tags</a>
      </div>

      <!-- Custom URL Form -->
      <div data-menu-form-target="customForm">
        <%= form_with model: [:dashboard, @menu_item], local: false, data: { controller: "menu-item-form", action: "submit->menu-item-form#submit" } do |form| %>
          <%= form.hidden_field :location, value: @current_location %>

          <div class="form-group mb-3">
            <%= form.label :label, class: "label text-sm" %>
            <%= form.text_field :label, class: "input input-bordered input-sm w-full", placeholder: "Menu Label" %>
          </div>

          <div class="form-group mb-3">
            <%= form.label :url, class: "label text-sm" %>
            <%= form.text_field :url, class: "input input-bordered input-sm w-full", placeholder: "/custom-url" %>
          </div>

          <button type="submit" class="btn btn-primary btn-sm w-full">Add Custom Item</button>
        <% end %>
      </div>

      <!-- Pages Form -->
      <div data-menu-form-target="pagesForm" class="hidden">
        <%= form_with model: [:dashboard, @menu_item], local: false, data: { controller: "menu-item-form", action: "submit->menu-item-form#submit" } do |form| %>
          <%= form.hidden_field :location, value: @current_location %>
          <%= form.hidden_field :source_type, value: "page" %>

          <div class="form-group mb-3">
            <%= form.label :source_id, "Select Page", class: "label text-sm" %>
            <%= form.select :source_id, options_from_collection_for_select(@pages, :id, :title), { prompt: "Choose a page..." }, { class: "select select-bordered select-sm w-full" } %>
          </div>

          <button type="submit" class="btn btn-primary btn-sm w-full">Add Page</button>
        <% end %>
      </div>

      <!-- Categories Form -->
      <div data-menu-form-target="categoriesForm" class="hidden">
        <%= form_with model: [:dashboard, @menu_item], local: false, data: { controller: "menu-item-form", action: "submit->menu-item-form#submit" } do |form| %>
          <%= form.hidden_field :location, value: @current_location %>
          <%= form.hidden_field :source_type, value: "category" %>

          <div class="form-group mb-3">
            <%= form.label :source_id, "Select Category", class: "label text-sm" %>
            <%= form.select :source_id, options_from_collection_for_select(@categories, :id, :name), { prompt: "Choose a category..." }, { class: "select select-bordered select-sm w-full" } %>
          </div>

          <button type="submit" class="btn btn-primary btn-sm w-full">Add Category</button>
        <% end %>
      </div>

      <!-- Tags Form -->
      <div data-menu-form-target="tagsForm" class="hidden">
        <%= form_with model: [:dashboard, @menu_item], local: false, data: { controller: "menu-item-form", action: "submit->menu-item-form#submit" } do |form| %>
          <%= form.hidden_field :location, value: @current_location %>
          <%= form.hidden_field :source_type, value: "tag" %>

          <div class="form-group mb-3">
            <%= form.label :source_id, "Select Tag", class: "label text-sm" %>
            <%= form.select :source_id, options_from_collection_for_select(@tags, :id, :name), { prompt: "Choose a tag..." }, { class: "select select-bordered select-sm w-full" } %>
          </div>

          <button type="submit" class="btn btn-primary btn-sm w-full">Add Tag</button>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Menu Structure: Full width on mobile, 80% on desktop -->
  <div class="w-full lg:w-4/5 bg-white rounded-lg shadow-sm border border-gray-200">
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <div>
          <h2 class="text-lg font-semibold text-gray-900 mb-2"><%= @locations[@current_location] %></h2>
          <p class="text-sm text-gray-600">Drag and drop to reorder menu items. Click Save to apply changes.</p>
        </div>
        <button data-controller="save-menu-order"
                data-action="click->save-menu-order#save"
                data-save-menu-order-location-value="<%= @current_location %>"
                data-save-menu-order-save-url-value="<%= save_order_dashboard_menu_items_path %>"
                class="btn btn-success">Save Changes</button>
      </div>

      <% if @menu_items.any? || @pending_items.any? %>
        <div id="menu-items-list" class="space-y-2" data-controller="sortable" data-sortable-url-value="<%= reorder_dashboard_menu_items_path %>" data-sortable-location-value="<%= @current_location %>">
          <!-- Existing Menu Items -->
          <% @menu_items.each do |menu_item| %>
            <div class="menu-item-card bg-gray-50 border border-gray-200 rounded-lg p-4 cursor-move hover:bg-gray-100 transition-colors"
                 data-id="<%= menu_item.id %>"
                 data-sortable-target="item">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <!-- Drag handle -->
                  <div class="drag-handle text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"></path>
                    </svg>
                  </div>

                  <!-- Menu item info -->
                  <div class="flex-1">
                    <div class="font-medium text-gray-900"><%= menu_item.label %></div>
                    <div class="text-sm text-gray-500"><%= menu_item.url %></div>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2">
                  <%= button_to "Delete", dashboard_menu_item_path(menu_item),
                      method: :delete,
                      data: { turbo_confirm: "Are you sure you want to delete this menu item?" },
                      class: "btn btn-sm btn-ghost text-red-600 hover:text-red-800" %>
                </div>
              </div>
            </div>
          <% end %>

          <!-- Pending Menu Items -->
          <% @pending_items.each_with_index do |pending_item, index| %>
            <div class="menu-item-card bg-yellow-50 border border-yellow-200 rounded-lg p-4 cursor-move hover:bg-yellow-100 transition-colors"
                 data-id="pending-<%= pending_item['temp_id'] %>"
                 data-sortable-target="item"
                 data-pending="true">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <!-- Drag handle -->
                  <div class="drag-handle text-yellow-400 hover:text-yellow-600">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M7 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM7 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 2a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 8a2 2 0 1 0 0 4 2 2 0 0 0 0-4zM13 14a2 2 0 1 0 0 4 2 2 0 0 0 0-4z"></path>
                    </svg>
                  </div>

                  <!-- Menu item info -->
                  <div class="flex-1">
                    <div class="font-medium text-gray-900">
                      <%= pending_item['label'] %>
                      <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full ml-2">Pending</span>
                    </div>
                    <div class="text-sm text-gray-500"><%= pending_item['url'] %></div>
                  </div>
                </div>

                <!-- Actions -->
                <div class="flex items-center space-x-2">
                  <button type="button"
                          class="btn btn-sm btn-ghost text-red-600 hover:text-red-800"
                          data-controller="delete-pending-item"
                          data-action="click->delete-pending-item#delete"
                          data-delete-pending-item-temp-id-value="<%= pending_item['temp_id'] %>"
                          data-delete-pending-item-location-value="<%= @current_location %>">
                    Delete
                  </button>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No menu items in <%= @locations[@current_location] %></h3>
          <p class="text-gray-600 mb-4">Get started by creating your first menu item using the form on the left.</p>
        </div>
      <% end %>
    </div>
  </div>
</div>


