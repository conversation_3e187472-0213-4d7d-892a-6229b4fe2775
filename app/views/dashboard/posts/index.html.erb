<h1>Posts</h1>

<div class="overflow-x-auto">
  <table class="table table-zebra w-full">
    <thead>
      <tr>
        <th>Title</th>
        <th>Category</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% @posts.each do |post| %>
        <tr>
          <td><%= post.title %></td>
          <td><%= post.category&.name %></td>
          <td>
            <% if post.published? %>
              <div class="badge badge-success">Published</div>
            <% elsif post.published_at && post.published_at > Time.current %>
              <div class="badge badge-warning">Scheduled</div>
            <% else %>
              <div class="badge badge-ghost">Draft</div>
            <% end %>
          </td>
          <td>
            <%= link_to "Show", post, class: "btn btn-xs btn-ghost", target: "_blank" %>
            <%= link_to "Edit", edit_dashboard_post_path(post), class: "btn btn-xs btn-info" %>
            <%= button_to "Delete", dashboard_post_path(post), method: :delete, data: { turbo_confirm: "Are you sure?" }, class: "btn btn-xs btn-error" %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<br>

<%= link_to "New Post", new_dashboard_post_path, class: "btn btn-primary" %>
