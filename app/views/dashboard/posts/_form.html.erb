<%= form_with(model: [:dashboard, post], data: { controller: "slug publish autosave", autosave_key_value: "dashboard_post_#{post.id || 'new'}", action: "submit->autosave#clear" }) do |form| %>
  <% if post.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(post.errors.count, "error") %> prohibited this post from being saved:</h2>
      <ul>
        <% post.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :title, class: "label" do %>
      <span class="label-text">Title</span>
    <% end %>
    <%= form.text_field :title, class: "input input-bordered w-full max-w-xs", data: { slug_target: "source", action: "input->slug#generate" } %>
  </div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :slug, class: "label" do %>
      <span class="label-text">Slug</span>
    <% end %>
    <%= form.text_field :slug, class: "input input-bordered w-full max-w-xs", data: { slug_target: "target" } %>
  </div>

  <div class="form-control mb-4">
    <%= form.label :content, class: "label" do %>
      <span class="label-text">Content</span>
    <% end %>
    <%= form.rich_text_area :content, class: "textarea textarea-bordered w-full h-auto" %>
  </div>

  <div class="form-control w-full max-w-xs mb-4" data-controller="media-picker">
    <%= form.label :featured_image, class: "label" do %>
      <span class="label-text">Featured Image</span>
    <% end %>
    
    <% if post.featured_image.attached? %>
      <div class="mb-2" data-media-picker-target="preview">
        <%= image_tag post.featured_image, class: "max-w-xs rounded" %>
      </div>
    <% else %>
      <div class="mb-2" data-media-picker-target="preview"></div>
    <% end %>
    
    <button type="button" class="btn btn-sm btn-primary" data-action="click->media-picker#openModal">
      Select from Library
    </button>
    
    <%= form.hidden_field :featured_image_media_id, data: { media_picker_target: "selectedInput" } %>
  </div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :category_id, class: "label" do %>
      <span class="label-text">Category</span>
    <% end %>
    <%= form.collection_select :category_id, Category.all, :id, :name, {}, { class: "select select-bordered" } %>
  </div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :tag_ids, "Tags", class: "label" do %>
      <span class="label-text">Tags</span>
    <% end %>
    <%= form.collection_select :tag_ids, Tag.all, :id, :name, {}, { multiple: true, class: "select select-bordered h-32" } %>
  </div>

  <div class="form-control mb-4">
    <label class="label">
      <span class="label-text">Status</span>
    </label>
    <div class="flex gap-4">
      <label class="label cursor-pointer gap-2">
        <span class="label-text">Draft</span>
        <input type="radio" name="post_status" value="draft" class="radio radio-primary" data-action="publish#toggle" <%= post.published_at.nil? ? "checked" : "" %> />
      </label>
      <label class="label cursor-pointer gap-2">
        <span class="label-text">Publish Now</span>
        <input type="radio" name="post_status" value="publish_now" class="radio radio-primary" data-action="publish#toggle" <%= post.published_at && post.published_at <= Time.current ? "checked" : "" %> />
      </label>
      <label class="label cursor-pointer gap-2">
        <span class="label-text">Schedule</span>
        <input type="radio" name="post_status" value="schedule" class="radio radio-primary" data-action="publish#toggle" <%= post.published_at && post.published_at > Time.current ? "checked" : "" %> />
      </label>
    </div>
  </div>

  <div class="form-control w-full max-w-xs mb-4 hidden" data-publish-target="dateContainer">
    <%= form.label :published_at, class: "label" do %>
      <span class="label-text">Publish Date</span>
    <% end %>
    <%= form.datetime_field :published_at, class: "input input-bordered", data: { publish_target: "dateInput" } %>
  </div>

  <div class="divider">SEO Settings</div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :meta_title, class: "label" do %>
      <span class="label-text">Meta Title</span>
    <% end %>
    <%= form.text_field :meta_title, class: "input input-bordered w-full max-w-xs", placeholder: "Leave blank to use post title" %>
  </div>

  <div class="form-control mb-4">
    <%= form.label :meta_description, class: "label" do %>
      <span class="label-text">Meta Description</span>
    <% end %>
    <%= form.text_area :meta_description, rows: 3, class: "textarea textarea-bordered w-full", placeholder: "Brief description for search engines" %>
  </div>

  <div class="mt-6">
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>
