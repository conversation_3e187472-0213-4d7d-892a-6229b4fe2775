<h1>Pages</h1>

<div class="overflow-x-auto">
  <table class="table table-zebra w-full">
    <thead>
      <tr>
        <th>Title</th>
        <th>Slug</th>
        <th>Published</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <% @pages.each do |page| %>
        <tr>
          <td><%= page.title %></td>
          <td><%= page.slug %></td>
          <td>
            <% if page.published? %>
              <div class="badge badge-success">Yes</div>
            <% else %>
              <div class="badge badge-ghost">No</div>
            <% end %>
          </td>
          <td>
            <%= link_to "Show", page, class: "btn btn-xs btn-ghost", target: "_blank" %>
            <%= link_to "Edit", edit_dashboard_page_path(page), class: "btn btn-xs btn-info" %>
            <%= button_to "Delete", dashboard_page_path(page), method: :delete, data: { turbo_confirm: "Are you sure?" }, class: "btn btn-xs btn-error" %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<br>

<%= link_to "New Page", new_dashboard_page_path, class: "btn btn-primary" %>
