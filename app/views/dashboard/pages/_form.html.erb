<%= form_with(model: [:dashboard, page], data: { controller: "slug" }) do |form| %>
  <% if page.errors.any? %>
    <div style="color: red">
      <h2><%= pluralize(page.errors.count, "error") %> prohibited this page from being saved:</h2>
      <ul>
        <% page.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :title, class: "label" do %>
      <span class="label-text">Title</span>
    <% end %>
    <%= form.text_field :title, class: "input input-bordered w-full max-w-xs", data: { slug_target: "source", action: "input->slug#generate" } %>
  </div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :slug, class: "label" do %>
      <span class="label-text">Slug</span>
    <% end %>
    <%= form.text_field :slug, class: "input input-bordered w-full max-w-xs", data: { slug_target: "target" } %>
  </div>

  <div class="form-control mb-4">
    <%= form.label :content, class: "label" do %>
      <span class="label-text">Content</span>
    <% end %>
    <%= form.rich_text_area :content, class: "textarea textarea-bordered h-24" %>
  </div>

  <div class="form-control mb-4">
    <label class="label cursor-pointer justify-start gap-4">
      <span class="label-text">Published</span>
      <%= form.check_box :published, class: "checkbox checkbox-primary" %>
    </label>
  </div>

  <div class="divider">SEO Settings</div>

  <div class="form-control w-full max-w-xs mb-4">
    <%= form.label :meta_title, class: "label" do %>
      <span class="label-text">Meta Title</span>
    <% end %>
    <%= form.text_field :meta_title, class: "input input-bordered w-full max-w-xs", placeholder: "Leave blank to use page title" %>
  </div>

  <div class="form-control mb-4">
    <%= form.label :meta_description, class: "label" do %>
      <span class="label-text">Meta Description</span>
    <% end %>
    <%= form.text_area :meta_description, rows: 3, class: "textarea textarea-bordered w-full", placeholder: "Brief description for search engines" %>
  </div>

  <div class="mt-6">
    <%= form.submit class: "btn btn-primary" %>
  </div>
<% end %>
