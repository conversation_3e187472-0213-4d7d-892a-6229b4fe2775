<% set_meta_tags title: @post.meta_title.presence || @post.title,
                  description: @post.meta_description.presence || truncate(strip_tags(@post.content.to_s), length: 160) %>

<article class="post">
  <% if @post.featured_image.attached? %>
    <div class="featured-image-show">
      <%= image_tag @post.featured_image, style: "max-width: 100%; height: auto; margin-bottom: 1rem;" %>
    </div>
  <% end %>
  <header>
    <h1><%= @post.title %></h1>
    <p class="meta">
      Published on <%= @post.published_at.to_date %>
      <% if @post.category %>
        in <%= @post.category.name %>
      <% end %>
    </p>
    <% if @post.tags.any? %>
      <div class="tags">
        Tags: <%= @post.tags.map(&:name).join(", ") %>
      </div>
    <% end %>
  </header>

  <div class="content">
    <%= simple_format @post.content %>
  </div>
</article>

<%= link_to "Back to Posts", posts_path %>
