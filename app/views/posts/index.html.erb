<h1>Blog Posts</h1>

<div class="posts">
  <% @posts.each do |post| %>
    <article class="post-preview">
      <% if post.featured_image.attached? %>
        <div class="featured-image">
          <%= image_tag post.featured_image, style: "max-width: 100%; height: auto;" %>
        </div>
      <% end %>
      <h2><%= link_to post.title, post_path(post) %></h2>
      <p class="meta">
        Published on <%= post.published_at.to_date %>
        <% if post.category %>
          in <%= post.category.name %>
        <% end %>
      </p>
      <div class="excerpt">
        <%= truncate(post.content.to_plain_text, length: 200) %>
      </div>
    </article>
  <% end %>
</div>
