@import "tailwindcss";

@property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
}

@source not "./daisyui{,*}.mjs";

@plugin "./daisyui.mjs" {
    themes: silk --default, abyss --prefersdark;
}

@custom-variant dark (&:where([data-theme=abyss], [data-theme=abyss] *));

/* Optional for custom themes – Docs: https://daisyui.com/docs/themes/#how-to-add-a-new-custom-theme */
@plugin "./daisyui-theme.mjs" {
    /* Custom themes here */
}

/* Restore semantic HTML defaults that Tailwind removes, for rich text editing */
@layer components {
    .lexxy-editor__content {

        ul,
        ol,
        dl {
            list-style: revert;
            margin: revert;
            padding: revert;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-size: revert;
            font-weight: revert;
            margin: revert;
        }

        strong {
            font-weight: revert;
        }

        em {
            font-style: revert;
        }
    }
}

/* Custom Styles Preserved from previous application.css */

/* Toast Notifications */
#flash-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.flash-toast {
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    animation: slideIn 0.3s ease-out;
    min-width: 300px;
}

.flash-toast.notice {
    background-color: #10b981;
    /* Emerald 500 */
}

.flash-toast.alert {
    background-color: #ef4444;
    /* Red 500 */
}

.flash-toast button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.8;
}

.flash-toast button:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Custom Confirm Modal */
.confirm-modal {
    border: none;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 400px;
    width: 90%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

.confirm-modal::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

.confirm-modal form {
    padding: 24px;
}

.confirm-modal h3 {
    margin-top: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.confirm-modal p {
    color: #4b5563;
    margin-bottom: 24px;
}

.confirm-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.confirm-actions button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
}

.confirm-actions button:hover {
    background-color: #f3f4f6;
}

.confirm-actions button.danger {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.confirm-actions button.danger:hover {
    background-color: #dc2626;
}

/* Text Alignment */
.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

/* Lexxy Content */
.lexxy-content {
    margin-top: 1rem;
}

.lexxy-content img {
    max-width: 100%;
    height: auto;
}

.hidden {
    display: none;
}