@import "tailwindcss";

@property --radialprogress {
    syntax: "<percentage>";
    inherits: true;
    initial-value: 0%;
}

@source not "./daisyui{,*}.mjs";

@plugin "./daisyui.mjs" {
    themes: silk --default, abyss --prefersdark;
}

@custom-variant dark (&:where([data-theme=abyss], [data-theme=abyss] *));

/* Optional for custom themes – Docs: https://daisyui.com/docs/themes/#how-to-add-a-new-custom-theme */
@plugin "./daisyui-theme.mjs" {
    /* Custom themes here */
}

/* Restore semantic HTML defaults that Tailwind removes, for rich text editing */
@layer components {
    .lexxy-editor__content {
        ul,
        ol,
        dl {
            list-style: revert;
            margin: revert;
            padding: revert;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            font-size: revert;
            font-weight: revert;
            margin: revert;
        }

        strong {
            font-weight: revert;
        }

        em {
            font-style: revert;
        }
    }
    /* Pagy */
    .pagy {
    --B: 1;
    --H: 0;
    --S: 0;
    --L: 50;
    --spacing: 0.125rem;
    --padding: 0.75rem;
    --rounding: 1.75rem;
    --border-width: 0rem;
    --font-size: 0.875rem;
    --font-weight: 600;
    --line-height: 1.75;

    --text:               hsl(var(--H) var(--S) calc(var(--L) - (30 * var(--B))));
    --text-hover:         hsl(var(--H) var(--S) calc(var(--L) - (33 * var(--B))));
    --text-current:       hsl(var(--H) var(--S) calc(100 * (var(--B) + 1)));
    --background:         hsl(var(--H) var(--S) calc(var(--L) + (30 * var(--B))));
    --background-hover:   hsl(var(--H) var(--S) calc(var(--L) + (20 * var(--B))));
    --background-current: hsl(var(--H) var(--S) var(--L));
    --background-input:   hsl(var(--H) var(--S) calc(var(--L) + (45 * var(--B))));
    --opacity: 1;

    @apply flex space-x-[var(--spacing)] font-[var(--font-weight)]
    text-[length:var(--font-size)] text-[var(--text)]
    leading-[var(--line-height)];

    a:not([role="separator"]) { /* all but gaps */
      @apply block rounded-[var(--rounding)] px-[var(--padding)] py-[calc(var(--padding)/3)] bg-[var(--background)]
      border-solid  border-[var(--background-current)] border-[length:var(--border-width)] opacity-[var(--opacity)];
    }

    a[href]:hover { /* all links on hover */
      @apply bg-[var(--background-hover)] text-[var(--text-hover)];
    }

    a:not([href]) { /* all but links */
      @apply cursor-default
    }

    a[role="link"]:not([aria-current]) { /* disabled links */
      @apply opacity-[calc(var(--opacity)*.6)];
    }

    a[aria-current] {  /* current page */
      @apply bg-[var(--background-current)] text-[var(--text-current)];
    }

    label {
      @apply inline-block whitespace-nowrap rounded-[var(--rounding)] px-[var(--padding)]
      py-[calc(var(--padding)/3-var(--border-width))]
      bg-[var(--background)] border-solid border-[length:var(--border-width)] border-[var(--background-current)];

      input {
        @apply text-[var(--text)] text-[length:var(--font-size)] leading-[var(--line-height)] rounded-[calc(var(--rounding)/2)]
        font-[var(--font-weight)] bg-[var(--background-input)] border-[length:var(--border-width)] border-[var(--background-current)];
      }
    }
  }
}

/* Custom Styles Preserved from previous application.css */

/* Toast Notifications */
#flash-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.flash-toast {
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 15px;
    animation: slideIn 0.3s ease-out;
    min-width: 300px;
}

.flash-toast.notice {
    background-color: #10b981;
    /* Emerald 500 */
}

.flash-toast.alert {
    background-color: #ef4444;
    /* Red 500 */
}

.flash-toast button {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.8;
}

.flash-toast button:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Custom Confirm Modal */
.confirm-modal {
    border: none;
    border-radius: 12px;
    padding: 0;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 400px;
    width: 90%;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

.confirm-modal::backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

.confirm-modal form {
    padding: 24px;
}

.confirm-modal h3 {
    margin-top: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.confirm-modal p {
    color: #4b5563;
    margin-bottom: 24px;
}

.confirm-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.confirm-actions button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
}

.confirm-actions button:hover {
    background-color: #f3f4f6;
}

.confirm-actions button.danger {
    background-color: #ef4444;
    color: white;
    border-color: #ef4444;
}

.confirm-actions button.danger:hover {
    background-color: #dc2626;
}

/* Text Alignment */
.text-left {
    text-align: left;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

/* Lexxy Content */
.lexxy-content {
    margin-top: 1rem;
}

.lexxy-content img {
    max-width: 100%;
    height: auto;
}

.hidden {
    display: none;
}