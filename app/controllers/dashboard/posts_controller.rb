module Dashboard
  class PostsController < BaseController
    before_action :set_post, only: %i[ edit update destroy ]

    def index
      @posts = Post.all.order(created_at: :desc)
    end

    def new
      @post = Post.new
    end

    def create
      @post = Post.new(post_params)
      @post.user = Current.user
      handle_post_status

      # Handle media selection from library
      if params[:post][:featured_image_media_id].present?
        attach_media_if_selected(@post, :featured_image, params[:post][:featured_image_media_id])
      end

      if @post.save
        redirect_to dashboard_posts_path, notice: "Post created successfully"
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      @post.assign_attributes(post_params)
      handle_post_status

      # Handle media selection from library
      if params[:post][:featured_image_media_id].present?
        attach_media_if_selected(@post, :featured_image, params[:post][:featured_image_media_id])
      end

      if @post.save
        redirect_to dashboard_posts_path, notice: "Post updated successfully"
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @post.destroy
      redirect_to dashboard_posts_path, notice: "Post deleted successfully"
    end

    private

    def set_post
      @post = Post.find(params[:id])
    end

    def post_params
      params.require(:post).permit(:title, :content, :published_at, :slug, :category_id, :featured_image, :meta_title, :meta_description, tag_ids: [])
    end

    def handle_post_status
      case params[:post_status]
      when "publish_now"
        @post.published_at = Time.current
      when "draft"
        @post.published_at = nil
      end
    end

    def attach_media_if_selected(record, attachment_name, media_id)
      return if media_id.blank?

      media_item = MediaItem.find_by(id: media_id)
      return unless media_item&.file&.attached?

      record.public_send(attachment_name).attach(media_item.file.blob)
    end
  end
end
