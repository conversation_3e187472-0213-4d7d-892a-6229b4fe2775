module Dashboard
  class MediaItemsController < BaseController
    before_action :set_media_item, only: [ :update, :destroy ]

    def index
      @media_items = MediaItem.recent
      @media_items = @media_items.where(file_type: params[:filter]) if params[:filter].present?
      @media_items = @media_items.where("title LIKE ?", "%#{params[:search]}%") if params[:search].present?
      @pagy, @media_items = pagy(@media_items, items: 24)
    end

    def create
      uploaded_files = params[:files] || [ params[:media_item][:file] ]
      @media_items = []
      errors = []

      allowed_content_types = %w[image/jpeg image/png image/gif image/webp video/mp4 application/pdf image/vnd.microsoft.icon image/x-icon]
      max_image_file_size = 2.megabytes
      max_pdf_file_size = 10.megabytes

      uploaded_files.compact.each do |file|
        current_max_size = if file.content_type.start_with?("image/") || file.content_type.include?("icon")
          max_image_file_size
        elsif file.content_type == "application/pdf"
          max_pdf_file_size
        else
          max_pdf_file_size
        end
        if file.size > current_max_size
          errors << "#{file.original_filename}: File is too large (maximum is #{current_max_size / 1.megabyte} MB)."
          next
        end

        unless allowed_content_types.include?(file.content_type)
          errors << "#{file.original_filename}: File type is not allowed."
          next
        end

        media_item = MediaItem.new(file: file)
        if media_item.save
          @media_items << media_item
        else
          # This will catch additional model-level validations (e.g., from Active Storage)
          errors << "#{file.original_filename}: #{media_item.errors.full_messages.join(', ')}"
        end
      end

      respond_to do |format|
        if errors.empty?
          format.html { redirect_to dashboard_media_items_path, notice: "#{@media_items.count} file(s) uploaded successfully." }
          format.json {
            render json: {
              success: true,
              media_items: @media_items.map { |m| {
                id: m.id,
                url: url_for(m.file),
                title: m.title
              }}
            }
          }
        else
          format.html { redirect_to dashboard_media_items_path, alert: "Some files failed to upload: #{errors.join('; ')}" }
          format.json { render json: { success: false, errors: errors }, status: :unprocessable_entity }
        end
      end
    end

    def update
      if @media_item.update(media_item_params)
        redirect_to dashboard_media_items_path, notice: "Media updated successfully."
      else
        redirect_to dashboard_media_items_path, alert: @media_item.errors.full_messages.join(", ")
      end
    end

    def destroy
      @media_item.destroy
      redirect_to dashboard_media_items_path, notice: "Media deleted successfully."
    end

    private

    def set_media_item
      @media_item = MediaItem.find(params[:id])
    end

    def media_item_params
      params.fetch(:media_item, {}).permit(:title, :alt_text)
    end
  end
end
