module Dashboard
  class LogsController < BaseController
    # Skip CanCanCan's resource loading, as this controller does not map to a model.
    skip_load_and_authorize_resource

    LOG_PATH = Rails.root.join("log")

    before_action :set_log_files
    before_action :set_current_log_path, only: [ :index, :download, :clear ]

    def index
      if @current_log_path && File.exist?(@current_log_path)
        lines = File.readlines(@current_log_path).reverse
        @pagy, @log_lines = pagy_array(lines, items: 100)
      else
        @log_lines = [ "Log file '#{@current_log}' not found." ]
        @pagy, @log_lines = pagy_array(@log_lines, items: 100)
      end
    end

    def download
      if @current_log_path && File.exist?(@current_log_path)
        send_file @current_log_path, filename: @current_log
      else
        redirect_to dashboard_logs_path, alert: "Log file '#{@current_log}' not found."
      end
    end

    def clear
      if @current_log_path && File.exist?(@current_log_path)
        File.truncate(@current_log_path, 0)
        redirect_to dashboard_logs_path(log_file: @current_log), notice: "Log file '#{@current_log}' has been cleared."
      else
        redirect_to dashboard_logs_path, alert: "Log file '#{@current_log}' not found."
      end
    end

    private

    def set_log_files
      @log_files = Dir.glob(LOG_PATH.join("*.log")).map { |path| File.basename(path) }.sort
    end

    def set_current_log_path
      @current_log = params[:log_file] || @log_files.find { |f| f == "#{Rails.env}.log" } || @log_files.first
      return unless @current_log

      @current_log_path = LOG_PATH.join(@current_log)
    end
  end
end
