module Dashboard
  class TagsController < BaseController
    before_action :set_tag, only: %i[ edit update destroy ]

    def index
      @tags = Tag.all.order(:name)
    end

    def new
      @tag = Tag.new
    end

    def create
      @tag = Tag.new(tag_params)
      if @tag.save
        redirect_to dashboard_tags_path, notice: "Tag created successfully"
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @tag.update(tag_params)
        redirect_to dashboard_tags_path, notice: "Tag updated successfully"
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @tag.destroy
      redirect_to dashboard_tags_path, notice: "Tag deleted successfully"
    end

    private

    def set_tag
      @tag = Tag.find(params[:id])
    end

    def tag_params
      params.require(:tag).permit(:name, :slug)
    end
  end
end
