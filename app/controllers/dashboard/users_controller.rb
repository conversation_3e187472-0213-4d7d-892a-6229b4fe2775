module Dashboard
  class UsersController < BaseController
    skip_load_and_authorize_resource
    before_action :require_administrator
    before_action :set_user, only: [ :edit, :update, :destroy ]

    def index
      @users = User.all.order(created_at: :desc)
    end

    def new
      @user = User.new
    end

    def create
      @user = User.new(user_create_params)

      if @user.save
        redirect_to dashboard_users_path, notice: "User created successfully"
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      update_params = user_params
      if update_params[:password].blank?
        update_params.delete(:password)
        update_params.delete(:password_confirmation)
      end

      if @user.update(update_params)
        redirect_to dashboard_users_path, notice: "User updated successfully"
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      if @user == Current.user
        redirect_to dashboard_users_path, alert: "You cannot delete yourself"
      else
        @user.destroy
        redirect_to dashboard_users_path, notice: "User deleted successfully"
      end
    end

    private

    def set_user
      @user = User.find(params[:id])
    end

    def user_params
      params.require(:user).permit(:email_address, :role_id, :password, :password_confirmation)
    end

    def user_create_params
      params.require(:user).permit(:email_address, :password, :password_confirmation, :role_id)
    end

    def require_administrator
      unless Current.user&.administrator?
        redirect_to dashboard_root_path, alert: "Only administrators can manage users"
      end
    end
  end
end
