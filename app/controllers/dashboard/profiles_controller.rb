module Dashboard
  class ProfilesController < BaseController
    skip_load_and_authorize_resource

    def edit
      @user = Current.user
    end

    def update
      @user = Current.user

      # Handle avatar selection from media library
      if params[:user][:avatar_media_id].present?
        attach_media_if_selected(@user, :avatar, params[:user][:avatar_media_id])
      end

      # Remove password fields if blank (user doesn't want to change password)
      if params[:user][:password].blank?
        params[:user].delete(:password)
        params[:user].delete(:password_confirmation)
      end

      if @user.update(profile_params)
        redirect_to edit_dashboard_profile_path, notice: "Profile updated successfully"
      else
        render :edit, status: :unprocessable_entity
      end
    end

    private

    def profile_params
      params.require(:user).permit(:name, :email_address, :avatar, :password, :password_confirmation)
    end

    def attach_media_if_selected(record, attachment_name, media_id)
      return if media_id.blank?

      media_item = MediaItem.find_by(id: media_id)
      return unless media_item&.file&.attached?

      record.public_send(attachment_name).attach(media_item.file.blob)
    end
  end
end
