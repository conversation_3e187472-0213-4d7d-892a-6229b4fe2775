module Dashboard
  class MenuItemsController < BaseController
    before_action :set_menu_item, only: [ :destroy ]

    def index
      @current_location = params[:location] || "primary"
      @menu_items = MenuItem.by_location(@current_location).ordered
      @locations = MenuItem::LOCATIONS
      @menu_item = MenuItem.new(location: @current_location)

      # Load data for menu item sources
      @categories = Category.all.order(:name)
      @tags = Tag.all.order(:name)
      @pages = Page.published.order(:title)

      # Add pending menu items from session
      @pending_items = (session[:pending_menu_items] || []).select { |item| item["location"] == @current_location }
    end



    def create
      @menu_item = MenuItem.new(menu_item_params)

      # Handle different menu item sources
      if params[:menu_item][:source_type].present? && params[:menu_item][:source_id].present?
        case params[:menu_item][:source_type]
        when "category"
          category = Category.find(params[:menu_item][:source_id])
          @menu_item.label = category.name
          @menu_item.url = "/categories/#{category.slug}"
        when "tag"
          tag = Tag.find(params[:menu_item][:source_id])
          @menu_item.label = tag.name
          @menu_item.url = "/tags/#{tag.slug}"
        when "page"
          page = Page.find(params[:menu_item][:source_id])
          @menu_item.label = page.title
          @menu_item.url = "/pages/#{page.slug}"
        end
      end

      # Set position to be last in the location
      @menu_item.position = (MenuItem.where(location: @menu_item.location).maximum(:position) || 0) + 1

      # Store new menu item in session instead of saving immediately
      session[:pending_menu_items] ||= []
      session[:pending_menu_items] << {
        "label" => @menu_item.label,
        "url" => @menu_item.url,
        "location" => @menu_item.location,
        "position" => @menu_item.position,
        "source_type" => @menu_item.source_type,
        "source_id" => @menu_item.source_id,
        "temp_id" => SecureRandom.uuid
      }

      render json: { success: true, message: "Menu item added to pending changes." }
    end



    def destroy
      location = @menu_item.location
      @menu_item.destroy

      respond_to do |format|
        format.html { redirect_to dashboard_menu_items_path(location: location), notice: "Menu item deleted successfully." }
        format.json { render json: { success: true, message: "Menu item deleted successfully." } }
      end
    end

    def reorder
      # Store the new order in session for later saving
      session[:menu_order] ||= {}
      session[:menu_order][params[:location] || "primary"] = params[:menu_item_ids]

      render json: { success: true, message: "Order updated. Click Save to apply changes." }
    end

    def save_order
      location = params[:location] || "primary"

      ActiveRecord::Base.transaction do
        # First, save any pending menu items and create a mapping
        pending_to_db_id_map = {}
        pending_items = (session[:pending_menu_items] || []).select { |item| item["location"] == location }

        pending_items.each do |item_data|
          menu_item = MenuItem.new(
            label: item_data["label"],
            url: item_data["url"],
            location: item_data["location"],
            position: 1, # Will be updated below based on order
            source_type: item_data["source_type"],
            source_id: item_data["source_id"]
          )

          if menu_item.save
            # Map the temp_id to the actual database ID
            pending_to_db_id_map[item_data["temp_id"]] = menu_item.id
          else
            Rails.logger.error "Failed to save menu item: #{menu_item.errors.full_messages}"
          end
        end

        # Remove saved pending items for this location
        session[:pending_menu_items] = (session[:pending_menu_items] || []).reject { |item| item["location"] == location }

        # Then handle reordering with the updated IDs
        menu_item_ids = session.dig(:menu_order, location)
        if menu_item_ids
          menu_item_ids.each_with_index do |id, index|
            actual_id = id

            # If this is a pending item, use the mapped database ID
            if id.to_s.start_with?("pending-")
              temp_id = id.to_s.sub("pending-", "")
              actual_id = pending_to_db_id_map[temp_id]
              next unless actual_id # Skip if mapping failed
            end

            # Use find_by to avoid exceptions for deleted items
            menu_item = MenuItem.find_by(id: actual_id)
            if menu_item && menu_item.location == location
              menu_item.update(position: index + 1)
            end
          end

          # Clear the saved order from session
          session[:menu_order]&.delete(location)
        end
      end

      render json: { success: true, message: "Menu changes saved successfully!" }
    end

    def remove_pending
      temp_id = params[:temp_id]
      location = params[:location] || "primary"

      if session[:pending_menu_items]
        # Remove the pending item with the matching temp_id
        session[:pending_menu_items].reject! { |item| item["temp_id"] == temp_id }

        # Clean up empty session data
        session[:pending_menu_items] = nil if session[:pending_menu_items].empty?

        render json: { success: true, message: "Pending menu item removed." }
      else
        render json: { success: false, error: "No pending items found." }
      end
    end

    private

    def set_menu_item
      @menu_item = MenuItem.find(params[:id])
    end

    def menu_item_params
      params.require(:menu_item).permit(:label, :url, :location, :source_type, :source_id)
    end
  end
end
