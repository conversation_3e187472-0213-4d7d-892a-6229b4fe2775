module Dashboard
  class PagesController < BaseController
    before_action :set_page, only: %i[ edit update destroy ]

    def index
      @pages = Page.all.order(created_at: :desc)
    end

    def new
      @page = Page.new
    end

    def create
      @page = Page.new(page_params)
      @page.user = Current.user
      if @page.save
        redirect_to dashboard_pages_path, notice: "Page created successfully"
      else
        render :new, status: :unprocessable_entity
      end
    end

    def edit
    end

    def update
      if @page.update(page_params)
        redirect_to dashboard_pages_path, notice: "Page updated successfully"
      else
        render :edit, status: :unprocessable_entity
      end
    end

    def destroy
      @page.destroy
      redirect_to dashboard_pages_path, notice: "Page deleted successfully"
    end

    private

    def set_page
      @page = Page.find(params[:id])
    end

    def page_params
      params.require(:page).permit(:title, :content, :slug, :published, :meta_title, :meta_description)
    end
  end
end
