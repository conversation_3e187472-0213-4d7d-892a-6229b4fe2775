class Dashboard::RolesController < Dashboard::BaseController
  before_action :set_role, only: %i[ edit update destroy ]

  def index
    @roles = Role.all.order(:name)
  end

  def new
    @role = Role.new
  end

  def create
    @role = Role.new(role_params)
    if @role.save
      redirect_to dashboard_roles_path, notice: "Role was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
  end

  def update
    if @role.update(role_params)
      redirect_to dashboard_roles_path, notice: "Role was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    if @role.system_defined?
      redirect_to dashboard_roles_path, alert: "System roles cannot be deleted."
    else
      @role.destroy
      redirect_to dashboard_roles_path, notice: "Role was successfully deleted."
    end
  end

  private

  def set_role
    @role = Role.find(params[:id])
  end

  def role_params
    params.require(:role).permit(:name, permissions_attributes: [ :id, :action, :resource, :_destroy ])
  end
end
