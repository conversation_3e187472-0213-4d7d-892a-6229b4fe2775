module Dashboard
  class SiteConfigsController < BaseController
    def edit
      @site_config = SiteConfig.instance
    end

    def update
      @site_config = SiteConfig.instance

      # Handle media selection from library
      if params[:site_config][:logo_media_id].present?
        attach_media_if_selected(@site_config, :logo, params[:site_config][:logo_media_id])
      end
      if params[:site_config][:favicon_media_id].present?
        attach_media_if_selected(@site_config, :favicon, params[:site_config][:favicon_media_id])
      end

      if @site_config.update(site_config_params)
        redirect_to edit_dashboard_site_config_path, notice: "Site settings updated successfully."
      else
        render :edit, status: :unprocessable_entity
      end
    end

    private

    def site_config_params
      params.require(:site_config).permit(:site_name, :site_description, :logo, :favicon, :allow_registration)
    end

    def attach_media_if_selected(record, attachment_name, media_id)
      return if media_id.blank?

      media_item = MediaItem.find_by(id: media_id)
      return unless media_item&.file&.attached?

      record.public_send(attachment_name).attach(media_item.file.blob)
    end
  end
end
