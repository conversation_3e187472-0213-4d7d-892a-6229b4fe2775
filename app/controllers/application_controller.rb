class ApplicationController < ActionController::Base
  include Pagy::Backend

  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  before_action :set_current_user
  before_action :load_site_config

  # CanCanCan authorization
  rescue_from CanCan::AccessDenied do |exception|
    redirect_to dashboard_root_path, alert: "You are not authorized to perform this action."
  end

  private
    def set_current_user
      Current.user = User.find_by(id: session[:user_id]) if session[:user_id]
    end

    def load_site_config
      @site_config = SiteConfig.instance
      @primary_menu_items = MenuItem.primary.ordered
      @footer_menu_items = MenuItem.footer.ordered
    end

    def current_ability
      @current_ability ||= Ability.new(Current.user)
    end
end
