class SessionsController < ApplicationController
  layout "auth"
  before_action :redirect_if_authenticated, only: [ :new, :create ]

  def new
  end

  def create
    if user = User.authenticate_by(email_address: params[:email_address], password: params[:password])
      session[:user_id] = user.id
      redirect_to dashboard_root_path, notice: "Signed in successfully"
    else
      redirect_to login_path, alert: "Invalid email or password"
    end
  end

  def destroy
    session[:user_id] = nil
    redirect_to login_path, notice: "Signed out successfully"
  end

  private

  def redirect_if_authenticated
    redirect_to dashboard_root_path, notice: "You are already logged in" if Current.user
  end
end
