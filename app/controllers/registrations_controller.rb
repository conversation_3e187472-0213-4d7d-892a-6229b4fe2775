class RegistrationsController < ApplicationController
  skip_before_action :verify_authenticity_token, only: [ :create ]
  before_action :check_registration_allowed, only: [ :new, :create ]
  before_action :redirect_if_authenticated, only: [ :new, :create ]

  def new
    @user = User.new
  end

  def create
    @user = User.new(user_params)
    @user.role = :contributor # Default role for new registrations

    if @user.save
      session[:user_id] = @user.id
      redirect_to dashboard_root_path, notice: "Welcome! Your account has been created successfully."
    else
      render :new, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:email_address, :password, :password_confirmation)
  end

  def check_registration_allowed
    site_config = SiteConfig.instance
    unless site_config.allow_registration
      redirect_to login_path, alert: "Registration is currently disabled"
    end
  end

  def redirect_if_authenticated
    redirect_to dashboard_root_path if Current.user
  end
end
