class SiteConfig < ApplicationRecord
  has_one_attached :logo
  has_one_attached :favicon

  validates :site_name, presence: true
  validates :allow_registration, inclusion: { in: [ true, false ] }

  validate :favicon_must_be_square

  # Singleton pattern - only one site config should exist
  def self.instance
    first_or_create!(site_name: "My Site")
  end

  private

  def favicon_must_be_square
    return unless favicon.attached?

    # ICO files can be problematic for ImageMagick to get dimensions from,
    # and they are purpose-built for favicons, so we'll skip the check for them.
    return if favicon.content_type == "image/vnd.microsoft.icon"

    begin
      # Read the attached file into MiniMagick for inspection
      image = MiniMagick::Image.read(favicon.download)
      if image.width != image.height
        errors.add(:favicon, "must be square (width and height must be equal)")
      end
    rescue MiniMagick::Error => e
      errors.add(:favicon, "could not be processed. It may be corrupted or in an unsupported format.")
      Rails.logger.error "Favicon processing failed: #{e.message}"
    end
  end
end
