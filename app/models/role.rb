class Role < ApplicationRecord
  has_many :users
  has_many :permissions, dependent: :destroy
  accepts_nested_attributes_for :permissions, allow_destroy: true

  validates :name, presence: true, uniqueness: true
  validates :slug, presence: true, uniqueness: true

  before_validation :generate_slug

  private

  def generate_slug
    self.slug = name.parameterize if slug.blank? && name.present?
  end
end
