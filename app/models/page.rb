class Page < ApplicationRecord
  belongs_to :user, optional: true
  has_rich_text :content
  has_one_attached :featured_image

  validates :title, presence: true
  validates :slug, presence: true, uniqueness: true

  before_validation :generate_slug

  scope :published, -> { where(published: true) }

  private

  def generate_slug
    self.slug = title.parameterize if slug.blank? && title.present?
  end
end
