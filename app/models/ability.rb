# frozen_string_literal: true

class Ability
  include CanCan::Ability

  def initialize(user)
    user ||= User.new # guest user (not logged in)
    return unless user.role

    user.role.permissions.each do |permission|
      action = permission.action.to_sym
      resource = permission.resource == "all" ? :all : permission.resource.constantize rescue permission.resource.to_sym

      case action
      when :manage_own
        can :manage, resource, user_id: user.id
      when :read_own
        can :read, resource, user_id: user.id
      when :update_own
        can :update, resource, user_id: user.id
      when :delete_own
        can :destroy, resource, user_id: user.id
      else
        can action, resource
      end
    end

    # Everyone can read public content (this remains static as it's for public access)
    can :read, Post, published_at: ->(time) { time <= Time.current }
    can :read, Page, published: true
  end
end
