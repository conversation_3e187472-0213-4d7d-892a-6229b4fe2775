class MediaItem < ApplicationRecord
  has_one_attached :file
  belongs_to :user, optional: true

  validates :title, presence: true
  validates :file, presence: true
  validates :file_type, inclusion: { in: %w[image document video], allow_nil: true }

  before_validation :set_file_type, on: :create
  before_validation :set_default_title, on: :create

  scope :images, -> { where(file_type: "image") }
  scope :documents, -> { where(file_type: "document") }
  scope :videos, -> { where(file_type: "video") }
  scope :recent, -> { order(created_at: :desc) }

  def image?
    file_type == "image"
  end

  def thumbnail_url
    return nil unless file.attached?
    if image? && file.representable?
      Rails.application.routes.url_helpers.rails_representation_url(
        file.representation(resize_to_limit: [ 300, 300 ]),
        only_path: true
      )
    else
      nil
    end
  end

  private

  def set_file_type
    return unless file.attached?

    content_type = file.content_type
    self.file_type = if content_type.start_with?("image/")
      "image"
    elsif content_type.start_with?("video/")
      "video"
    else
      "document"
    end
  end

  def set_default_title
    self.title ||= file.filename.to_s if file.attached?
  end
end
