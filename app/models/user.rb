class User < ApplicationRecord
  has_secure_password
  has_one_attached :avatar

  # Role-based access control
  # Role-based access control
  belongs_to :role

  validates :email_address, presence: true, uniqueness: true
  validates :role, presence: true

  delegate :administrator?, :editor?, :writer?, :contributor?, to: :role_inquirer

  def role_inquirer
    role.slug.inquiry
  end

  def display_name
    name.presence || email_address.split("@").first
  end
end
