class MenuItem < ApplicationRecord
  validates :label, presence: true
  validates :url, presence: true
  validates :position, numericality: { only_integer: true }, allow_nil: true
  validates :location, presence: true, inclusion: { in: %w[primary footer] }

  scope :ordered, -> { order(position: :asc) }
  scope :by_location, ->(location) { where(location: location) }
  scope :primary, -> { by_location("primary") }
  scope :footer, -> { by_location("footer") }

  # WordPress-style menu locations
  LOCATIONS = {
    "primary" => "Primary Menu (Header)",
    "footer" => "Footer Menu"
  }.freeze

  before_create :set_default_position

  # Get next position for a specific location
  def self.next_position(location = "primary")
    by_location(location).maximum(:position) || 0 + 1
  end

  # Reorder all menu items within a location to eliminate gaps
  def self.normalize_positions!(location = nil)
    scope = location ? by_location(location) : all
    scope.ordered.each_with_index do |item, index|
      item.update_column(:position, index + 1)
    end
  end

  # Move this item to a specific position within the same location and adjust others
  def move_to_position!(new_position)
    return if position == new_position

    transaction do
      if new_position > position
        # Moving down - shift items up within the same location
        MenuItem.by_location(location)
                .where("position > ? AND position <= ?", position, new_position)
                .update_all("position = position - 1")
      else
        # Moving up - shift items down within the same location
        MenuItem.by_location(location)
                .where("position >= ? AND position < ?", new_position, position)
                .update_all("position = position + 1")
      end

      update!(position: new_position)
    end
  end

  private

  def set_default_position
    self.position ||= self.class.next_position(self.location || "primary")
  end
end
