import { Turbo } from "@hotwired/turbo-rails"

// Use the new Turbo config API. The top-level Turbo.setConfirmMethod is deprecated.
// See Turbo docs: set Turbo.config.forms.confirm = confirmMethod
const confirmMethod = (message, element) => {
    const dialog = document.getElementById("turbo-confirm")
    if (!dialog) {
        // Fallback to native confirm if dialog markup not present
        return Promise.resolve(window.confirm(message))
    }

    const quote = dialog.querySelector("p")
    const confirmBtn = dialog.querySelector("button[value='confirm']")
    const cancelBtn = dialog.querySelector("button[value='cancel']")

    dialog.showModal()
    if (quote) quote.textContent = message

    return new Promise((resolve) => {
        if (confirmBtn) {
            confirmBtn.addEventListener("click", () => {
                resolve(true)
                dialog.close()
            }, { once: true })
        }

        if (cancelBtn) {
            cancelBtn.addEventListener("click", () => {
                resolve(false)
                dialog.close()
            }, { once: true })
        }
    })
}

// Assign to the Turbo config so the confirm is used by forms/actions
if (Turbo && Turbo.config && Turbo.config.forms) {
    Turbo.config.forms.confirm = confirmMethod
} else if (Turbo && Turbo.setConfirmMethod) {
    // Fallback for older Turbo versions (temporary)
    Turbo.setConfirmMethod(confirmMethod)
}
