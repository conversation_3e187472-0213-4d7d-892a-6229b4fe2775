import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  async submit(event) {
    event.preventDefault()
    
    const form = event.target
    const formData = new FormData(form)
    
    try {
      const response = await fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'Accept': 'application/json'
        }
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Show success message
        this.showNotification(data.message + ' Click Save Changes to apply.', 'info')

        // Reset form
        form.reset()

        // Enable the save button
        const saveButton = document.getElementById('save-menu-order')
        if (saveButton) {
          saveButton.disabled = false
          saveButton.classList.remove('btn-disabled')
        }

        // Refresh the page to show the pending item
        setTimeout(() => {
          window.location.reload()
        }, 1000) // Small delay to show the notification first
      } else {
        // Show error messages
        this.showNotification(data.errors.join(', '), 'error')
      }
    } catch (error) {
      console.error('Error:', error)
      this.showNotification('An error occurred while adding the menu item.', 'error')
    }
  }
  
  showNotification(message, type) {
    // Create a simple notification
    const notification = document.createElement('div')
    notification.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-md`
    notification.innerHTML = `
      <span>${message}</span>
      <button class="btn btn-sm btn-ghost" onclick="this.parentElement.remove()">×</button>
    `
    
    document.body.appendChild(notification)
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 5000)
  }
}
