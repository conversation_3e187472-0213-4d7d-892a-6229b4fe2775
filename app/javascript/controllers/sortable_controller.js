import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["item"]
    static values = { url: String, location: String }

    connect() {
        this.initializeSortable()
    }

    initializeSortable() {
        // Simple drag and drop implementation without external libraries
        this.itemTargets.forEach((item, index) => {
            item.draggable = true
            item.dataset.originalIndex = index
            
            item.addEventListener('dragstart', this.handleDragStart.bind(this))
            item.addEventListener('dragover', this.handleDragOver.bind(this))
            item.addEventListener('drop', this.handleDrop.bind(this))
            item.addEventListener('dragend', this.handleDragEnd.bind(this))
        })
    }

    handleDragStart(event) {
        this.draggedElement = event.target.closest('[data-sortable-target="item"]')
        this.draggedElement.classList.add('opacity-50')
        event.dataTransfer.effectAllowed = 'move'
        event.dataTransfer.setData('text/html', this.draggedElement.outerHTML)
    }

    handleDragOver(event) {
        event.preventDefault()
        event.dataTransfer.dropEffect = 'move'
        
        const afterElement = this.getDragAfterElement(event.clientY)
        const draggedElement = this.draggedElement
        
        if (afterElement == null) {
            this.element.appendChild(draggedElement)
        } else {
            this.element.insertBefore(draggedElement, afterElement)
        }
    }

    handleDrop(event) {
        event.preventDefault()
        this.updatePositions()
    }

    handleDragEnd(event) {
        if (this.draggedElement) {
            this.draggedElement.classList.remove('opacity-50')
            this.draggedElement = null
        }
    }

    getDragAfterElement(y) {
        const draggableElements = [...this.itemTargets.filter(item => item !== this.draggedElement)]
        
        return draggableElements.reduce((closest, child) => {
            const box = child.getBoundingClientRect()
            const offset = y - box.top - box.height / 2
            
            if (offset < 0 && offset > closest.offset) {
                return { offset: offset, element: child }
            } else {
                return closest
            }
        }, { offset: Number.NEGATIVE_INFINITY }).element
    }

    async updatePositions() {
        const menuItemIds = this.itemTargets.map(item => item.dataset.id)

        try {
            const response = await fetch(this.urlValue, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    menu_item_ids: menuItemIds,
                    location: this.locationValue
                })
            })

            const data = await response.json()

            if (response.ok) {
                this.showInfoMessage(data.message)
                // Enable the save button
                const saveButton = document.getElementById('save-menu-order')
                if (saveButton) {
                    saveButton.disabled = false
                    saveButton.classList.remove('btn-disabled')
                }
            } else {
                this.showErrorMessage()
            }
        } catch (error) {
            console.error('Error updating positions:', error)
            this.showErrorMessage()
        }
    }

    showInfoMessage(text) {
        // Create a temporary info message
        const message = document.createElement('div')
        message.className = 'alert alert-info fixed top-4 right-4 z-50 w-auto'
        message.innerHTML = `<span>${text}</span>`
        document.body.appendChild(message)

        setTimeout(() => {
            message.remove()
        }, 3000)
    }

    showSuccessMessage(text) {
        // Create a temporary success message
        const message = document.createElement('div')
        message.className = 'alert alert-success fixed top-4 right-4 z-50 w-auto'
        message.innerHTML = `<span>${text}</span>`
        document.body.appendChild(message)

        setTimeout(() => {
            message.remove()
        }, 3000)
    }

    showErrorMessage() {
        // Create a temporary error message
        const message = document.createElement('div')
        message.className = 'alert alert-error fixed top-4 right-4 z-50 w-auto'
        message.innerHTML = '<span>Failed to update menu order. Please try again.</span>'
        document.body.appendChild(message)
        
        setTimeout(() => {
            message.remove()
        }, 5000)
    }
}
