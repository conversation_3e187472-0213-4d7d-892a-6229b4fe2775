import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["dateContainer", "dateInput"]

    connect() {
        this.toggle()
    }

    toggle(event) {
        const status = event ? event.target.value : this.currentStatus()

        if (status === "schedule") {
            this.dateContainerTarget.classList.remove("hidden")
            this.dateInputTarget.disabled = false
        } else if (status === "publish_now") {
            this.dateContainerTarget.classList.add("hidden")
            this.dateInputTarget.disabled = false
            // Set to current time roughly, or let backend handle it if empty?
            // For now, let's set it to current time ISO string
            const now = new Date()
            now.setMinutes(now.getMinutes() - now.getTimezoneOffset())
            this.dateInputTarget.value = now.toISOString().slice(0, 16)
        } else {
            // Draft
            this.dateContainerTarget.classList.add("hidden")
            this.dateInputTarget.disabled = true
            this.dateInputTarget.value = ""
        }
    }

    currentStatus() {
        const checkedRadio = this.element.querySelector("input[name='post_status']:checked")
        return checkedRadio ? checkedRadio.value : "draft"
    }
}
