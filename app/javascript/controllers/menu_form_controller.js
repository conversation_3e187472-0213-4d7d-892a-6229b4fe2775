import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["customTab", "pagesTab", "categoriesTab", "tagsTab", 
                   "customForm", "pagesForm", "categoriesForm", "tagsForm"]

  connect() {
    this.showCustom()
  }

  showCustom() {
    this.hideAllForms()
    this.hideAllTabs()
    this.customTabTarget.classList.add("tab-active")
    this.customFormTarget.classList.remove("hidden")
  }

  showPages() {
    this.hideAllForms()
    this.hideAllTabs()
    this.pagesTabTarget.classList.add("tab-active")
    this.pagesFormTarget.classList.remove("hidden")
  }

  showCategories() {
    this.hideAllForms()
    this.hideAllTabs()
    this.categoriesTabTarget.classList.add("tab-active")
    this.categoriesFormTarget.classList.remove("hidden")
  }

  showTags() {
    this.hideAllForms()
    this.hideAllTabs()
    this.tagsTabTarget.classList.add("tab-active")
    this.tagsFormTarget.classList.remove("hidden")
  }

  hideAllForms() {
    this.customFormTarget.classList.add("hidden")
    this.pagesFormTarget.classList.add("hidden")
    this.categoriesFormTarget.classList.add("hidden")
    this.tagsFormTarget.classList.add("hidden")
  }

  hideAllTabs() {
    this.customTabTarget.classList.remove("tab-active")
    this.pagesTabTarget.classList.remove("tab-active")
    this.categoriesTabTarget.classList.remove("tab-active")
    this.tagsTabTarget.classList.remove("tab-active")
  }
}
