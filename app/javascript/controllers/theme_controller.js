import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["lightIcon", "darkIcon", "systemIcon"]

  connect() {
    this.setTheme(this.getTheme())
  }

  setLight() {
    localStorage.theme = "light"
    this.setTheme("light")
  }

  setDark() {
    localStorage.theme = "dark"
    this.setTheme("dark")
  }

  setSystem() {
    localStorage.removeItem("theme")
    this.setTheme(this.getTheme())
  }

  getTheme() {
    if (localStorage.theme === "dark" || (!("theme" in localStorage) && window.matchMedia("(prefers-color-scheme: dark)").matches)) {
      return "dark"
    } else {
      return "light"
    }
  }

  setTheme(theme) {
    const isDark = theme === "dark"
    document.documentElement.setAttribute("data-theme", isDark ? "abyss" : "silk")
    this.lightIconTarget.classList.toggle("hidden", isDark)
    this.darkIconTarget.classList.toggle("hidden", !isDark)
  }
}