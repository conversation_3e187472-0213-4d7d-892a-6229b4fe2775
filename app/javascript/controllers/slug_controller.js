import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["source", "target"]

    connect() {
        this.slugify = this.slugify.bind(this)
    }

    generate() {
        const sourceValue = this.sourceTarget.value
        const targetValue = this.targetTarget.value

        // Only update if target is empty or matches the slugified source (allowing manual override)
        if (targetValue === "" || targetValue === this.slugify(this.previousSource)) {
            this.targetTarget.value = this.slugify(sourceValue)
        }

        this.previousSource = sourceValue
    }

    slugify(text) {
        if (!text) return ""
        return text.toString().toLowerCase()
            .replace(/\s+/g, '-')           // Replace spaces with -
            .replace(/[^\w\-]+/g, '')       // Remove all non-word chars
            .replace(/\-\-+/g, '-')         // Replace multiple - with single -
            .replace(/^-+/, '')             // Trim - from start of text
            .replace(/-+$/, '')             // Trim - from end of text
    }
}
