import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["selectedInput", "preview"]
    static values = {
        previewClasses: { type: String, default: "max-w-xs rounded" }
    }

    connect() {
        // Media picker controller initialized
        // Ensure this element has a stable UID so the modal can target the correct picker instance
        if (!this.element.dataset.mediaPickerUid) {
            this.element.dataset.mediaPickerUid = `mp-${Math.floor(Math.random() * 1e9)}`
        }
    }

    openModal() {
        const modal = document.getElementById('media_picker_modal')
        if (modal) {
            // mark this picker as the active one for the modal to target
            modal.dataset.activePicker = this.element.dataset.mediaPickerUid
            modal.showModal()
        }
    }

    selectMedia(event) {
        const mediaId = event.currentTarget.dataset.mediaId
        const mediaUrl = event.currentTarget.dataset.mediaUrl
        const mediaTitle = event.currentTarget.dataset.mediaTitle

        // Update hidden input with media ID
        if (this.hasSelectedInputTarget) {
            this.selectedInputTarget.value = mediaId
        }

        // Update preview
        if (this.hasPreviewTarget) {
            this.previewTarget.innerHTML = `
        <div class="relative inline-block">
          <img src="${mediaUrl}" alt="${mediaTitle}" class="${this.previewClassesValue}">
        </div>
      `
        }

        // Close modal
        const modal = document.getElementById('media_picker_modal')
        if (modal) {
            modal.close()
        }
    }

    // Helper to show a warning message near the preview (manages its own element)
    setWarning(message) {
        if (!this.hasPreviewTarget) return
        const preview = this.previewTarget
        let warn = this.element.querySelector('[data-media-picker-warning]')
        if (!warn) {
            warn = document.createElement('div')
            warn.setAttribute('data-media-picker-warning', 'true')
            warn.className = 'text-yellow-600 text-sm mt-2'
            preview.insertAdjacentElement('afterend', warn)
        }
        warn.textContent = message
        warn.style.display = 'block'
    }

    clearWarning() {
        const warn = this.element.querySelector('[data-media-picker-warning]')
        if (warn) {
            warn.textContent = ''
            warn.style.display = 'none'
        }
    }

    clearSelection() {
        if (this.hasSelectedInputTarget) {
            this.selectedInputTarget.value = ""
        }
        if (this.hasPreviewTarget) {
            this.previewTarget.innerHTML = ""
        }
    }
}
