import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static values = { key: String }

    connect() {
        this.restore()
        this.interval = setInterval(() => this.save(), 5000) // Autosave every 5 seconds
        this.element.addEventListener("submit", this.clear.bind(this))
    }

    disconnect() {
        clearInterval(this.interval)
    }

    save() {
        const data = new FormData(this.element)
        const value = Object.fromEntries(data.entries())
        // Remove sensitive or heavy fields if necessary (like files)
        delete value["post[featured_image]"]
        delete value["authenticity_token"]

        localStorage.setItem(this.keyValue, JSON.stringify(value))
        this.showSavingIndicator()
    }

    restore() {
        const saved = localStorage.getItem(this.keyValue)
        if (saved) {
            const value = JSON.parse(saved)
            for (const [key, val] of Object.entries(value)) {
                const input = this.element.querySelector(`[name='${key}']`)
                if (input && input.type !== "file" && input.type !== "hidden") {
                    if (input.type === "radio" || input.type === "checkbox") {
                        if (input.value === val) input.checked = true
                    } else {
                        input.value = val
                    }
                }
            }
        }
    }

    clear() {
        localStorage.removeItem(this.keyValue)
    }

    showSavingIndicator() {
        // Optional: Add a visual indicator
        // console.log("Autosaved")
    }
}
