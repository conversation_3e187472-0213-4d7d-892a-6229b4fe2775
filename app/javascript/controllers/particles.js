// Lightweight particle background for auth pages (controller version)
// Initializes on Turbo page load and cleans up before Turbo replaces the DOM.

// Tunable options
const CONFIG = {
  densityFactor: 1.6,    // increased density (1.0 default -> 1.6)
  speedScale: 1.0,       // speed multiplier
  particleColor: { r: 170, g: 200, b: 255 },
  backgroundTop: 'rgba(6,11,23,0.92)',
  backgroundBottom: 'rgba(3,7,15,0.98)'
};

let canvas = null;
let ctx = null;
let particles = [];
let animationId = null;
let DPR = 1;
let last = 0;
let resizeHandler = null;

function rand(min, max) {
  return Math.random() * (max - min) + min;
}

function resize() {
  if (!canvas) return;
  DPR = Math.max(1, window.devicePixelRatio || 1);
  canvas.width = Math.floor(window.innerWidth * DPR);
  canvas.height = Math.floor(window.innerHeight * DPR);
  canvas.style.width = window.innerWidth + 'px';
  canvas.style.height = window.innerHeight + 'px';
  ctx.setTransform(DPR, 0, 0, DPR, 0, 0);
}

function createParticles(count = Math.round((window.innerWidth * window.innerHeight) / (90000 / CONFIG.densityFactor))) {
  particles = [];
  for (let i = 0; i < count; i++) {
    particles.push({
      x: Math.random() * window.innerWidth,
      y: Math.random() * window.innerHeight,
      vx: rand(-0.15, 0.15) * CONFIG.speedScale,
      vy: rand(-0.05, 0.05) * CONFIG.speedScale,
      r: rand(0.4, 2.2),
      alpha: rand(0.12, 0.9),
      twinkle: Math.random() * 100
    });
  }
}

function tick(now) {
  if (!ctx) return;
  const dt = Math.min(40, now - last);
  last = now;
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  // subtle gradient background
  const g = ctx.createLinearGradient(0, 0, 0, canvas.height / DPR);
  g.addColorStop(0, CONFIG.backgroundTop);
  g.addColorStop(1, CONFIG.backgroundBottom);
  ctx.fillStyle = g;
  ctx.fillRect(0, 0, canvas.width / DPR, canvas.height / DPR);

  ctx.globalCompositeOperation = 'lighter';
  for (let p of particles) {
    p.x += p.vx * dt;
    p.y += p.vy * dt;
    // wrap
    if (p.x < -10) p.x = window.innerWidth + 10;
    if (p.x > window.innerWidth + 10) p.x = -10;
    if (p.y < -10) p.y = window.innerHeight + 10;
    if (p.y > window.innerHeight + 10) p.y = -10;

    // twinkle
    p.twinkle += dt * 0.02;
    const a = p.alpha * (0.75 + 0.25 * Math.sin(p.twinkle));

    ctx.beginPath();
    const gradient = ctx.createRadialGradient(p.x, p.y, 0, p.x, p.y, p.r * 6);
    gradient.addColorStop(0, `rgba(255,255,255,${a})`);
    gradient.addColorStop(0.2, `rgba(${CONFIG.particleColor.r},${CONFIG.particleColor.g},${CONFIG.particleColor.b},${a * 0.35})`);
    gradient.addColorStop(1, `rgba(${CONFIG.particleColor.r},${CONFIG.particleColor.g},${CONFIG.particleColor.b},0)`);
    ctx.fillStyle = gradient;
    ctx.arc(p.x, p.y, p.r * 6, 0, Math.PI * 2);
    ctx.fill();
  }
  ctx.globalCompositeOperation = 'source-over';
  animationId = requestAnimationFrame(tick);
}

function start() {
  if (!canvas) return;
  resize();
  createParticles();
  last = performance.now();
  if (!animationId) animationId = requestAnimationFrame(tick);
}

function stop() {
  if (animationId) cancelAnimationFrame(animationId);
  animationId = null;
  particles = [];
}

function initParticles() {
  // If already initialized, stop and re-init
  stop();
  // find canvas in current DOM
  canvas = document.getElementById('auth-particles-canvas');
  if (!canvas) return;
  ctx = canvas.getContext('2d');

  // attach resize handler
  if (!resizeHandler) {
    resizeHandler = () => {
      resize();
      createParticles();
    };
  }
  window.addEventListener('resize', resizeHandler, { passive: true });

  // Respect reduced motion
  const mq = window.matchMedia('(prefers-reduced-motion: reduce)');
  if (mq && mq.matches) {
    resize();
    createParticles(60);
    tick(performance.now());
  } else {
    start();
  }
}

function destroyParticles() {
  stop();
  if (resizeHandler) {
    window.removeEventListener('resize', resizeHandler);
  }
  canvas = null;
  ctx = null;
}

// Initialize on first load and on Turbo navigation
document.addEventListener('turbo:load', () => initParticles());
document.addEventListener('turbo:before-render', () => destroyParticles());

// Also attempt to initialize immediately (covers classic full page loads)
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  initParticles();
} else {
  document.addEventListener('DOMContentLoaded', () => initParticles(), { once: true });
}

export {};
