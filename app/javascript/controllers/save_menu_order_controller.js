import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { location: String, saveUrl: String }

  async save(event) {
    event.preventDefault()
    
    try {
      const response = await fetch(this.saveUrlValue, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: JSON.stringify({ 
          location: this.locationValue 
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        this.showNotification(data.message, 'success')
        // Reload page to show persisted changes
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      } else {
        this.showNotification(data.message || 'Failed to save menu order', 'error')
      }
    } catch (error) {
      console.error('Error saving order:', error)
      this.showNotification('Failed to save menu order. Please try again.', 'error')
    }
  }

  showNotification(message, type) {
    const notification = document.createElement('div')
    notification.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-md`
    notification.innerHTML = `
      <span>${message}</span>
      <button class="btn btn-sm btn-ghost" onclick="this.parentElement.remove()">×</button>
    `

    document.body.appendChild(notification)

    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 5000)
  }
}
