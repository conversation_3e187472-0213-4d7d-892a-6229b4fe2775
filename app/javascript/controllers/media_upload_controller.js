import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["fileInput", "preview", "submitBtn", "dropzone", "status"]

    connect() {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.body.addEventListener(eventName, this.preventDefaults, false)
        })
    }

    disconnect() {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.body.removeEventListener(eventName, this.preventDefaults, false)
        })
    }

    preventDefaults(e) {
        e.preventDefault()
        e.stopPropagation()
    }

    dragOver(e) {
        this.preventDefaults(e)
        if (this.hasDropzoneTarget) {
            this.dropzoneTarget.classList.add('border-primary', 'bg-base-200')
        }
    }

    dragLeave(e) {
        this.preventDefaults(e)
        if (this.hasDropzoneTarget) {
            this.dropzoneTarget.classList.remove('border-primary', 'bg-base-200')
        }
    }

    drop(e) {
        this.preventDefaults(e)
        if (this.hasDropzoneTarget) {
            this.dropzoneTarget.classList.remove('border-primary', 'bg-base-200')
        }

        const files = e.dataTransfer.files
        if (files.length > 0 && this.hasFileInputTarget) {
            this.fileInputTarget.files = files
            this.handleFilesAndSubmit({ target: this.fileInputTarget })
        }
    }

    async handleSubmit(event) {
        event.preventDefault()

        const form = event.target
        const formData = new FormData(form)

        if (this.hasStatusTarget) {
            this.statusTarget.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Uploading...'
            this.statusTarget.classList.add('text-primary')
        }

        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                    'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
                }
            })

            const data = await response.json()

            if (data.success && data.media_items && data.media_items.length > 0) {
                // Add uploaded items to the media grid
                const mediaGrid = document.getElementById('media_picker_grid')
                if (mediaGrid) {
                    data.media_items.forEach(media => {
                        const mediaCard = this.createMediaCard(media)
                        mediaGrid.insertBefore(mediaCard, mediaGrid.firstChild)
                    })
                }

                // Clear the preview and file input
                if (this.hasPreviewTarget) {
                    this.previewTarget.innerHTML = ''
                }
                if (this.hasFileInputTarget) {
                    this.fileInputTarget.value = ''
                }

                // Auto-select the first uploaded image
                const firstMedia = data.media_items[0]
                window.selectMediaFromPicker(firstMedia.id, firstMedia.url, firstMedia.title)
            }
        } catch (error) {
            console.error('Upload error:', error)
            alert('Upload failed. Please try again.')
        } finally {
            if (this.hasStatusTarget) {
                this.statusTarget.innerHTML = ''
            }
        }
    }

    createMediaCard(media) {
        const div = document.createElement('div')
        div.className = 'cursor-pointer hover:opacity-75 transition border-2 border-transparent hover:border-primary rounded'
        div.onclick = () => window.selectMediaFromPicker(media.id, media.url, media.title)

        div.innerHTML = `
      <div class="card bg-base-100 shadow">
        <figure class="h-24 bg-base-200">
          <img src="${media.url}" class="object-cover w-full h-full" alt="${media.title}">
        </figure>
        <div class="card-body p-2">
          <p class="text-xs truncate" title="${media.title}">${media.title}</p>
        </div>
      </div>
    `

        return div
    }

    handleFilesAndSubmit(event) {
        const files = event.target.files

        if (files.length === 0) return

        // Show preview
        this.showPreview(files)

        // Auto-submit the form
        const form = event.target.closest('form')
        if (!form) return

        // Check if we're in the media picker modal (has submit event handler)
        const isInModal = form.dataset.action && form.dataset.action.includes('submit->media-upload#handleSubmit')

        if (isInModal) {
            // In modal - use AJAX submit via handleSubmit
            if (this.hasStatusTarget) {
                this.statusTarget.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Uploading ' + files.length + ' file(s)...'
                this.statusTarget.classList.add('text-primary')
            }

            // Trigger the submit event which will be caught by handleSubmit
            form.requestSubmit()
        } else {
            // On media library page - use normal submit (page reload)
            if (this.hasStatusTarget) {
                this.statusTarget.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Uploading ' + files.length + ' file(s)...'
                this.statusTarget.classList.add('text-primary')
            }

            form.submit()
        }
    }

    showPreview(files) {
        if (!this.hasPreviewTarget) return

        this.previewTarget.innerHTML = ""
        const previewContainer = document.createElement("div")
        previewContainer.className = "grid grid-cols-3 gap-2"

        Array.from(files).forEach((file) => {
            const filePreview = document.createElement("div")
            filePreview.className = "relative"

            if (file.type.startsWith("image/")) {
                const img = document.createElement("img")
                img.className = "w-full h-20 object-cover rounded"
                img.src = URL.createObjectURL(file)
                filePreview.appendChild(img)
            } else {
                const fileIcon = document.createElement("div")
                fileIcon.className = "w-full h-20 bg-base-200 rounded flex items-center justify-center"
                fileIcon.innerHTML = `<span class="text-2xl">📄</span>`
                filePreview.appendChild(fileIcon)
            }

            const fileName = document.createElement("p")
            fileName.className = "text-xs truncate mt-1"
            fileName.textContent = file.name
            filePreview.appendChild(fileName)

            previewContainer.appendChild(filePreview)
        })

        this.previewTarget.appendChild(previewContainer)
    }

    handleFiles(event) {
        this.showPreview(event.target.files)
    }
}
