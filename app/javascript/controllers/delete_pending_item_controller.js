import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = { tempId: String, location: String }

  async delete(event) {
    event.preventDefault()

    // Use the Turbo confirm system
    const confirmed = await this.showTurboConfirm('Are you sure you want to remove this pending menu item?')
    if (!confirmed) {
      return
    }
    
    try {
      const response = await fetch('/dashboard/menu_items/remove_pending', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          temp_id: this.tempIdValue,
          location: this.locationValue
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Show success message
        this.showNotification(data.message, 'success')
        
        // Refresh the page to show updated pending items
        setTimeout(() => {
          window.location.reload()
        }, 500)
      } else {
        this.showNotification(data.error || 'Failed to remove pending item', 'error')
      }
    } catch (error) {
      console.error('Error:', error)
      this.showNotification('An error occurred while removing the pending item.', 'error')
    }
  }
  
  showNotification(message, type) {
    // Create a simple notification
    const notification = document.createElement('div')
    notification.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-md`
    notification.innerHTML = `
      <span>${message}</span>
      <button class="btn btn-sm btn-ghost" onclick="this.parentElement.remove()">×</button>
    `

    document.body.appendChild(notification)

    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove()
      }
    }, 3000)
  }

  async showTurboConfirm(message) {
    // Import the confirm method from the custom_confirm.js
    const { Turbo } = await import("@hotwired/turbo-rails")

    if (Turbo && Turbo.config && Turbo.config.forms && Turbo.config.forms.confirm) {
      return await Turbo.config.forms.confirm(message, null)
    }

    // Fallback to native confirm if Turbo confirm not available
    return Promise.resolve(window.confirm(message))
  }
}
