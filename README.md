# Rorschools

This is a Content Management System (CMS) built with Ruby on Rails.

---

### System dependencies

*   **Ruby:** (Please specify your Ruby version here, e.g., 3.2.2)
*   **Database:** The application uses SQLite3 for development and testing. The production setup can be configured for databases like MySQL or PostgreSQL.
*   **ImageMagick:** Required for image processing (Active Storage).

---

### Getting Started

1.  **Clone the repository:**
    ```bash
    git clone <your-repository-url>
    cd rorschools
    ```

2.  **Install dependencies:**
    ```bash
    bundle install
    ```

3.  **Configure environment variables:**
    Copy the Rails master key (`config/master.key`) if it's not in the repository. This is needed to decrypt `config/credentials.yml.enc`.

4.  **Database setup:**
    ```bash
    bin/rails db:create
    bin/rails db:migrate
    bin/rails db:seed
    ```
    This will create the database, run migrations, and seed it with initial data, including a default admin user.

    *   **Default Admin Email:** `<EMAIL>`
    *   **Default Admin Password:** `password123`

    > [!WARNING]
    > Please change the default password immediately after your first login.

5.  **Start the server:**
    ```bash
    bin/dev
    ```
    The application will be available at `http://localhost:3000`.

---

### How to run the test suite

Run the full test suite with the following command:
```bash
bin/rails test
```

---

### Deployment

This application is configured for deployment using Kamal. The deployment configuration can be found in `config/deploy.yml`.

To deploy, run:
```bash
bin/kamal deploy
```
