Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  get "login" => "sessions#new", as: :login
  post "login" => "sessions#create"
  delete "logout" => "sessions#destroy", as: :logout

  get "register" => "registrations#new", as: :register
  post "register" => "registrations#create"

  namespace :dashboard do
    root to: "dashboard#index"
    resource :profile, only: [ :edit, :update ]
    resource :site_config, only: [ :edit, :update ]
    resources :menu_items, only: [ :index, :create, :destroy ] do
      patch :reorder, on: :collection
      patch :save_order, on: :collection
      delete :remove_pending, on: :collection
    end
    resources :media_items, only: [ :index, :create, :update, :destroy ]
    resources :posts
    resources :pages
    resources :categories
    resources :tags
    resources :users, only: [ :index, :new, :create, :edit, :update, :destroy ]
    resources :roles
    get "logs", to: "logs#index"
    get "logs/download", to: "logs#download"
    delete "logs/clear", to: "logs#clear"
  end

  resources :posts, only: [ :index, :show ]
  resources :pages, only: [ :show ], param: :slug

  root "posts#index"
end
