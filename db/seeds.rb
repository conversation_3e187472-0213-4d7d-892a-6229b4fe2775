# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create roles
administrator_role = Role.find_or_create_by!(slug: "administrator") { |r| r.name = "Administrator"; r.system_defined = true }
editor_role = Role.find_or_create_by!(slug: "editor") { |r| r.name = "Editor"; r.system_defined = true }
writer_role = Role.find_or_create_by!(slug: "writer") { |r| r.name = "Writer"; r.system_defined = true }
contributor_role = Role.find_or_create_by!(slug: "contributor") { |r| r.name = "Contributor"; r.system_defined = true }

# Create permissions
# Administrator: Manage everything
Permission.find_or_create_by!(role: administrator_role, action: "manage", resource: "all")

# Editor: Manage content
[ "Post", "Page", "Category", "Tag", "MediaItem" ].each do |resource|
  Permission.find_or_create_by!(role: editor_role, action: "manage", resource: resource)
end

# Writer: Create and update own content
[ "Post", "Page" ].each do |resource|
  Permission.find_or_create_by!(role: writer_role, action: "create", resource: resource)
  Permission.find_or_create_by!(role: writer_role, action: "update_own", resource: resource)
  Permission.find_or_create_by!(role: writer_role, action: "read", resource: resource)
end

# Create default administrator user
User.find_or_create_by!(email_address: "<EMAIL>") do |user|
  user.password = "password123"
  user.password_confirmation = "password123"
  user.role = administrator_role
end

# Create site config
SiteConfig.find_or_create_by!(id: 1) do |config|
  config.site_name = "My CMS"
  config.site_description = "A powerful content management system"
  config.allow_registration = true
end

puts "✅ Seed data created successfully!"
puts "📧 Default admin user: <EMAIL>"
puts "🔑 Default password: password123"
puts "⚠️  Please change the default password after first login!"
