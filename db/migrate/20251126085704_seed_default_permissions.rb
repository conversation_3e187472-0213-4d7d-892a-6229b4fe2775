class SeedDefaultPermissions < ActiveRecord::Migration[8.1]
  def up
    # Administrator
    admin = Role.find_by(slug: "administrator")
    if admin
      Permission.create!(role: admin, action: "manage", resource: "all")
    end

    # Editor
    editor = Role.find_by(slug: "editor")
    if editor
      %w[Post Page Category Tag MediaItem MenuItem].each do |resource|
        Permission.create!(role: editor, action: "manage", resource: resource)
      end
      Permission.create!(role: editor, action: "read", resource: "SiteConfig")
    end

    # Writer
    writer = Role.find_by(slug: "writer")
    if writer
      Permission.create!(role: writer, action: "manage_own", resource: "Post")
      Permission.create!(role: writer, action: "manage_own", resource: "Page")
      Permission.create!(role: writer, action: "create", resource: "MediaItem")
      Permission.create!(role: writer, action: "manage_own", resource: "MediaItem")
      %w[Category Tag MenuItem SiteConfig].each do |resource|
        Permission.create!(role: writer, action: "read", resource: resource)
      end
      # Writer can read all MediaItems? Old ability said: can :read, [Category, Tag, MediaItem, MenuItem, SiteConfig]
      Permission.create!(role: writer, action: "read", resource: "MediaItem")
    end

    # Contributor
    contributor = Role.find_by(slug: "contributor")
    if contributor
      # can [:create, :read, :update], Post, user_id: user.id
      Permission.create!(role: contributor, action: "create", resource: "Post")
      Permission.create!(role: contributor, action: "read_own", resource: "Post")
      Permission.create!(role: contributor, action: "update_own", resource: "Post")

      Permission.create!(role: contributor, action: "create", resource: "Page")
      Permission.create!(role: contributor, action: "read_own", resource: "Page")
      Permission.create!(role: contributor, action: "update_own", resource: "Page")

      Permission.create!(role: contributor, action: "create", resource: "MediaItem")
      Permission.create!(role: contributor, action: "manage_own", resource: "MediaItem")

      %w[Category Tag MenuItem SiteConfig].each do |resource|
        Permission.create!(role: contributor, action: "read", resource: resource)
      end
      Permission.create!(role: contributor, action: "read", resource: "MediaItem")
    end
  end

  def down
    Permission.delete_all
  end
end
