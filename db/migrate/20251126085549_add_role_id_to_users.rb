class AddRoleIdToUsers < ActiveRecord::Migration[8.1]
  def up
    add_reference :users, :role, null: true, foreign_key: true

    # Create system roles
    roles = {
      contributor: Role.find_or_create_by!(name: "Contributor", slug: "contributor", system_defined: true),
      writer: Role.find_or_create_by!(name: "Writer", slug: "writer", system_defined: true),
      editor: Role.find_or_create_by!(name: "Editor", slug: "editor", system_defined: true),
      administrator: Role.find_or_create_by!(name: "Administrator", slug: "administrator", system_defined: true)
    }

    # Migrate existing users
    User.find_each do |user|
      # user.role returns a string like "contributor" because of the enum
      role_slug = user.role
      if role_slug.present? && roles[role_slug.to_sym]
        user.update_column(:role_id, roles[role_slug.to_sym].id)
      end
    end

    # Enforce null: false after migration
    change_column_null :users, :role_id, false

    # Rename old role column to avoid conflicts but keep data
    rename_column :users, :role, :old_role_enum
  end

  def down
    rename_column :users, :old_role_enum, :role
    remove_reference :users, :role
  end
end
