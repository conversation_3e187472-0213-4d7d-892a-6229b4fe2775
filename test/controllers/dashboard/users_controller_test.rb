require "test_helper"

class Dashboard::UsersControllerTest < ActionDispatch::IntegrationTest
  setup do
    @admin = users(:admin)
    @editor = users(:editor)
    @contributor = users(:contributor)
  end

  # Authentication and Authorization Tests

  test "should redirect to login when not authenticated" do
    get dashboard_users_path
    assert_redirected_to login_path
    assert_equal "Please login first", flash[:alert]
  end

  test "should deny access to non-administrators" do
    login_as(@editor)
    get dashboard_users_path
    assert_redirected_to dashboard_root_path
    assert_equal "Only administrators can manage users", flash[:alert]
  end

  # Index Tests

  test "should get index as administrator" do
    login_as(@admin)
    get dashboard_users_path
    assert_response :success
    assert_select "h1", "User Management"
  end

  test "should list all users in index" do
    login_as(@admin)
    get dashboard_users_path
    assert_response :success
    assert_select "table tbody tr", User.count
  end

  # New Tests

  test "should get new user form as administrator" do
    login_as(@admin)
    get new_dashboard_user_path
    assert_response :success
    assert_select "h1", "Create New User"
    assert_select "form"
  end

  # Create Tests

  test "should create user as administrator" do
    login_as(@admin)

    assert_difference("User.count") do
      post dashboard_users_path, params: {
        user: {
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          role: "writer"
        }
      }
    end

    assert_redirected_to dashboard_users_path
    assert_equal "User created successfully", flash[:notice]

    new_user = User.find_by(email_address: "<EMAIL>")
    assert_equal "writer", new_user.role
  end

  test "should not create user with invalid data" do
    login_as(@admin)

    assert_no_difference("User.count") do
      post dashboard_users_path, params: {
        user: {
          email_address: "",
          password: "password123",
          password_confirmation: "password123",
          role: "writer"
        }
      }
    end

    assert_response :unprocessable_entity
  end

  test "should not create user as non-administrator" do
    login_as(@editor)

    assert_no_difference("User.count") do
      post dashboard_users_path, params: {
        user: {
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123",
          role: "writer"
        }
      }
    end

    assert_redirected_to dashboard_root_path
  end

  # Edit Tests

  test "should get edit as administrator" do
    login_as(@admin)
    get edit_dashboard_user_path(@contributor)
    assert_response :success
    assert_select "h1", "Edit User Role"
  end

  # Update Tests

  test "should update user role as administrator" do
    login_as(@admin)

    patch dashboard_user_path(@contributor), params: {
      user: { role: "writer" }
    }

    assert_redirected_to dashboard_users_path
    assert_equal "User updated successfully", flash[:notice]
    assert_equal "writer", @contributor.reload.role
  end

  test "should not update user as non-administrator" do
    login_as(@editor)
    original_role = @contributor.role

    patch dashboard_user_path(@contributor), params: {
      user: { role: "administrator" }
    }

    assert_redirected_to dashboard_root_path
    assert_equal original_role, @contributor.reload.role
  end

  # Destroy Tests

  test "should delete user as administrator" do
    login_as(@admin)

    assert_difference("User.count", -1) do
      delete dashboard_user_path(@contributor)
    end

    assert_redirected_to dashboard_users_path
    assert_equal "User deleted successfully", flash[:notice]
  end

  test "should not delete self" do
    login_as(@admin)

    assert_no_difference("User.count") do
      delete dashboard_user_path(@admin)
    end

    assert_redirected_to dashboard_users_path
    assert_equal "You cannot delete yourself", flash[:alert]
  end

  test "should not delete user as non-administrator" do
    login_as(@editor)

    assert_no_difference("User.count") do
      delete dashboard_user_path(@contributor)
    end

    assert_redirected_to dashboard_root_path
  end

  private

  def login_as(user)
    post login_path, params: {
      email_address: user.email_address,
      password: "password123"
    }
  end
end
