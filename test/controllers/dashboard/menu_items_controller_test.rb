require "test_helper"

class Dashboard::MenuItemsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @user = users(:admin)
    @menu_item = menu_items(:one)
    sign_in @user
  end

  test "should get index" do
    get dashboard_menu_items_url
    assert_response :success
  end



  test "should create menu_item with automatic position" do
    assert_no_difference("MenuItem.count") do
      post dashboard_menu_items_url, params: { menu_item: { label: "New Item", url: "/new", location: "primary" } }, as: :json
    end

    response_data = JSON.parse(response.body)
    assert response_data["success"]
    assert_equal "Menu item added to pending changes.", response_data["message"]

    # Check that item is stored in session
    assert_not_nil session[:pending_menu_items]
    assert_equal 1, session[:pending_menu_items].length
    pending_item = session[:pending_menu_items].first
    assert_equal "New Item", pending_item["label"]
    assert_equal "/new", pending_item["url"]
    assert_equal "primary", pending_item["location"]
  end



  test "should destroy menu_item" do
    assert_difference("MenuItem.count", -1) do
      delete dashboard_menu_item_url(@menu_item)
    end

    assert_redirected_to dashboard_menu_items_url(location: "primary")
  end

  test "should create menu_item from page" do
    page = Page.create!(title: "Test Page", slug: "test-page", published: true)

    assert_no_difference("MenuItem.count") do
      post dashboard_menu_items_url, params: {
        menu_item: {
          source_type: "page",
          source_id: page.id,
          location: "primary"
        }
      }, as: :json
    end

    response_data = JSON.parse(response.body)
    assert response_data["success"]

    # Check that item is stored in session with correct data from page
    pending_item = session[:pending_menu_items].first
    assert_equal "Test Page", pending_item["label"]
    assert_equal "/pages/test-page", pending_item["url"]
    assert_equal "page", pending_item["source_type"]
    assert_equal page.id, pending_item["source_id"]
  end

  test "should reorder menu items temporarily" do
    item1 = MenuItem.create!(label: "Item 1", url: "/item1", position: 1, location: "primary")
    item2 = MenuItem.create!(label: "Item 2", url: "/item2", position: 2, location: "primary")

    patch reorder_dashboard_menu_items_url, params: {
      menu_item_ids: [ item2.id, item1.id ],
      location: "primary"
    }, as: :json

    response_data = JSON.parse(response.body)
    assert response_data["success"]
    assert_includes response_data["message"], "Click Save to apply changes"
  end

  test "should save pending menu items" do
    # Create a pending item directly in the controller's session
    post dashboard_menu_items_url, params: { menu_item: { label: "Pending Item", url: "/pending", location: "primary" } }, as: :json

    # Verify it's in session
    assert_not_nil session[:pending_menu_items]
    assert_equal 1, session[:pending_menu_items].length

    # Then save the order
    assert_difference("MenuItem.count", 1) do
      patch save_order_dashboard_menu_items_url, params: { location: "primary" }, as: :json
    end

    response_data = JSON.parse(response.body)
    assert response_data["success"]
    assert_equal "Menu changes saved successfully!", response_data["message"]

    # Check that pending item was created
    new_item = MenuItem.find_by(label: "Pending Item")
    assert_not_nil new_item
    assert_equal "/pending", new_item.url
    assert_equal "primary", new_item.location

    # Check that session was cleared
    assert_empty (session[:pending_menu_items] || []).select { |item| item["location"] == "primary" }
  end

  private

  def sign_in(user)
    post login_url, params: { email_address: user.email_address, password: "password" }
  end
end
