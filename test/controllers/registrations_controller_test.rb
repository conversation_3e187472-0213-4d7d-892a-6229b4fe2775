require "test_helper"

class RegistrationsControllerTest < ActionDispatch::IntegrationTest
  setup do
    @site_config = SiteConfig.instance
  end

  # New Tests

  test "should get new registration form when registration is enabled" do
    @site_config.update(allow_registration: true)
    get register_path
    assert_response :success
    assert_select "h1", "Create Account"
    assert_select "form"
  end

  test "should redirect when registration is disabled" do
    @site_config.update(allow_registration: false)
    get register_path
    assert_redirected_to login_path
    assert_equal "Registration is currently disabled", flash[:alert]
  end

  test "should redirect authenticated users away from registration" do
    @site_config.update(allow_registration: true)
    login_as(users(:contributor))

    get register_path
    assert_redirected_to dashboard_root_path
  end

  # Create Tests

  test "should create user with contributor role when registration is enabled" do
    @site_config.update(allow_registration: true)

    assert_difference("User.count") do
      post register_path, params: {
        user: {
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end

    assert_redirected_to dashboard_root_path
    assert_equal "Welcome! Your account has been created successfully.", flash[:notice]

    new_user = User.find_by(email_address: "<EMAIL>")
    assert_equal "contributor", new_user.role
  end

  test "should not create user when registration is disabled" do
    @site_config.update(allow_registration: false)

    assert_no_difference("User.count") do
      post register_path, params: {
        user: {
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end

    assert_redirected_to login_path
    assert_equal "Registration is currently disabled", flash[:alert]
  end

  test "should not create user with invalid data" do
    @site_config.update(allow_registration: true)

    assert_no_difference("User.count") do
      post register_path, params: {
        user: {
          email_address: "",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end

    assert_response :unprocessable_entity
  end

  test "should not create user with mismatched passwords" do
    @site_config.update(allow_registration: true)

    assert_no_difference("User.count") do
      post register_path, params: {
        user: {
          email_address: "<EMAIL>",
          password: "password123",
          password_confirmation: "different"
        }
      }
    end

    assert_response :unprocessable_entity
  end

  test "should log in user after successful registration" do
    @site_config.update(allow_registration: true)

    post register_path, params: {
      user: {
        email_address: "<EMAIL>",
        password: "password123",
        password_confirmation: "password123"
      }
    }

    assert_not_nil session[:user_id]
    new_user = User.find_by(email_address: "<EMAIL>")
    assert_equal new_user.id, session[:user_id]
  end

  private

  def login_as(user)
    post login_path, params: {
      email_address: user.email_address,
      password: "password123"
    }
  end
end
