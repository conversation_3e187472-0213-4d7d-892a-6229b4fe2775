require "test_helper"

class SessionsControllerTest < ActionDispatch::IntegrationTest
  test "should get login page" do
    get login_url
    assert_response :success
  end

  test "should redirect to dashboard on successful login" do
    user = User.create!(email_address: "<EMAIL>", password: "password123")
    post login_url, params: { email_address: "<EMAIL>", password: "password123" }
    assert_redirected_to dashboard_root_url
  end

  test "should redirect to login on failed login" do
    post login_url, params: { email_address: "<EMAIL>", password: "wrongpassword" }
    assert_redirected_to login_url
  end

  test "should logout" do
    delete logout_url
    assert_redirected_to login_url
  end
end
