require "test_helper"

class UserTest < ActiveSupport::TestCase
  test "should have valid fixtures" do
    assert users(:admin).valid?
    assert users(:editor).valid?
    assert users(:writer).valid?
    assert users(:contributor).valid?
  end

  test "should require email address" do
    user = User.new(password: "password123", role: :contributor)
    assert_not user.valid?
    assert_includes user.errors[:email_address], "can't be blank"
  end

  test "should require unique email address" do
    user = User.new(
      email_address: users(:admin).email_address,
      password: "password123",
      role: :contributor
    )
    assert_not user.valid?
    assert_includes user.errors[:email_address], "has already been taken"
  end

  test "should have default role of contributor" do
    user = User.new(email_address: "<EMAIL>", password: "password123")
    assert_equal "contributor", user.role
  end

  test "should have role enum with correct values" do
    assert_equal 0, User.roles[:contributor]
    assert_equal 1, User.roles[:writer]
    assert_equal 2, User.roles[:editor]
    assert_equal 3, User.roles[:administrator]
  end

  test "should respond to role query methods" do
    assert users(:admin).administrator?
    assert users(:editor).editor?
    assert users(:writer).writer?
    assert users(:contributor).contributor?
  end

  test "should not be administrator by default" do
    user = User.new(email_address: "<EMAIL>", password: "password123")
    assert_not user.administrator?
  end

  test "should allow role changes" do
    user = users(:contributor)
    user.role = :writer
    assert user.save
    assert user.writer?
  end
end
