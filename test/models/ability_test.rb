require "test_helper"

class AbilityTest < ActiveSupport::TestCase
  test "administrator can manage everything" do
    ability = Ability.new(users(:admin))

    assert ability.can?(:manage, :all)
    assert ability.can?(:manage, User)
    assert ability.can?(:manage, Post)
    assert ability.can?(:manage, Page)
    assert ability.can?(:manage, Category)
    assert ability.can?(:manage, Tag)
    assert ability.can?(:manage, MediaItem)
    assert ability.can?(:manage, MenuItem)
    assert ability.can?(:manage, SiteConfig)
  end

  test "editor can manage content but not users" do
    ability = Ability.new(users(:editor))

    assert ability.can?(:manage, Post)
    assert ability.can?(:manage, Page)
    assert ability.can?(:manage, Category)
    assert ability.can?(:manage, Tag)
    assert ability.can?(:manage, MediaItem)
    assert ability.can?(:manage, MenuItem)
    assert ability.can?(:read, SiteConfig)

    assert ability.cannot?(:manage, User)
    assert ability.cannot?(:update, SiteConfig)
  end

  test "writer can manage own posts and pages" do
    ability = Ability.new(users(:writer))
    writer_post = Post.new(user: users(:writer))
    other_post = Post.new(user: users(:admin))

    assert ability.can?(:manage, writer_post)
    assert ability.cannot?(:manage, other_post)

    assert ability.can?(:read, Category)
    assert ability.can?(:read, Tag)
    assert ability.can?(:read, MediaItem)
    assert ability.cannot?(:manage, Category)
  end

  test "contributor can create and edit own posts but not publish" do
    ability = Ability.new(users(:contributor))
    contributor_post = Post.new(user: users(:contributor))
    other_post = Post.new(user: users(:admin))

    assert ability.can?(:create, Post)
    assert ability.can?(:read, contributor_post)
    assert ability.can?(:update, contributor_post)
    assert ability.cannot?(:publish, contributor_post)

    assert ability.cannot?(:manage, other_post)
    assert ability.can?(:read, Category)
    assert ability.cannot?(:manage, Category)
  end

  test "guest user has no permissions" do
    ability = Ability.new(nil)

    assert ability.cannot?(:manage, Post)
    assert ability.cannot?(:manage, Page)
    assert ability.cannot?(:manage, User)
  end

  test "everyone can read published pages" do
    published_page = Page.new(published: true)
    unpublished_page = Page.new(published: false)

    [ nil, users(:contributor), users(:writer), users(:editor), users(:admin) ].each do |user|
      ability = Ability.new(user)
      assert ability.can?(:read, published_page)
    end
  end
end
