# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

admin:
  email_address: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  role: admin

editor:
  email_address: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  role: editor

writer:
  email_address: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  role: writer

contributor:
  email_address: <EMAIL>
  password_digest: <%= BCrypt::Password.create("password") %>
  role: contributor
